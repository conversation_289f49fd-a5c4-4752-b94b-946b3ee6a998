const mongoose = require('mongoose');

// Define the schema for goods items
const GoodsSchema = new mongoose.Schema({
  quality: { type: String, required: true },
  design: { type: String, required: true },
  pieces: { type: Number, required: true },
  quantity: { type: Number, required: true },
  rate: { type: Number, required: true },
  amount: { type: Number, required: true }
}, { _id: false });

const InvoiceSchema = new mongoose.Schema({
  exporter: {
    name: { type: String, default: 'M/S Rachin Exports', required: true },
    address: { type: String, default: 'Madhosingh, Santravidas Nagar, Dist Bhadohi, U.P. India', required: true }
  },
  consignee: {
    name: { type: mongoose.Schema.Types.ObjectId, ref: 'Buyer', required: true },
    address: { type: String, required: true },
    country: { type: String, required: true }
  },
  invoiceNo: { type: String, required: true },
  invoiceDate: { type: String, required: true },
  exportersRef: { type: String },
  buyerOrderNo: { type: mongoose.Schema.Types.ObjectId, ref: 'BuyerOrder', required: true },
  buyerOrderDate: { type: String },
  gstNo: { type: String, required: true },
  panNo: { type: String, required: true },
  shippingDetails: {
    preCarriageBy: { type: String },
    placeOfReceipt: { type: String },
    vesselNo: { type: String },
    portOfLoading: { type: String, default: 'India' },
    countryOfOrigin: { type: String, default: 'India', required: true },
    countryOfDestination: { type: String, required: true },
    portOfDischarge: { type: String, required: true },
    finalDestination: { type: String, required: true },
    deliveryTerms: { type: String }
  },
  goodsDescription: { type: String, required: true },
  marksAndContNo: { type: String },
  kindOfPkgs: { type: String },
  rollNumbers: { type: String },
  area: { type: mongoose.Schema.Types.ObjectId, ref: 'SizeMaster', required: true },
  goodsDetails: [
    {
      quality: { type: String },
      design: { type: String },
      pieces: { type: Number },
      quantitySqMeter: { type: Number },
      rateFOB: { type: Number },
      amountFOB: { type: Number }
    }
  ],
  totalFOB: { type: Number },
  woolPercentage: { type: Number, default: 80 },
  cottonPercentage: { type: Number, default: 20 },
  grossWeight: { type: Number },
  netWeight: { type: Number },
  additionalCharges: {
    insurance: { type: Number },
    igst: { type: Number },
    igstPercentage: { type: Number }
  },
  finalAmount: { type: Number },
  amountInWords: { type: String },
  rexRegistrationNo: { type: String, default: 'INREX1502000776DG015', required: true },
  declaration: { type: String, default: 'We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.' },
  signature: { type: String },
  createdAt: { type: String, default: () => new Date().toISOString() }
});

module.exports = mongoose.model('Invoice', InvoiceSchema);