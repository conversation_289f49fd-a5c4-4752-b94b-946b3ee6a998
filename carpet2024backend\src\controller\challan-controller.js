const CreateChallanService = require("../services/challan-service");
const ContainerReceived = require("../model/phase-1/container-rvc");
const { CreateChallan } = require("../model/phase-2/createChallan");

const BillWholeseller = require("../model/phase-2/billWholeseller");
const { group } = require("console");
class CreateChallanController {
  constructor() {
    this.createChallanService = new CreateChallanService();
  }

  // async createChallan(req, res) {
  //   try {
  //     const challanData = req.body;
  //     const newChallan = await this.createChallanService.createChallan(
  //       challanData
  //     );
  //     if (newChallan) {
  //       newChallan.carpetList.forEach(async (elem) => {
  //         console.log(elem.barcodeNo);
  //         await ContainerReceived.updateOne(
  //           { "containerItem.GerCarpetNo": elem.barcodeNo }, // Filter to find the document with GerCarpetNo equal to '50001'
  //           { $set: { "containerItem.$.status": "sale" } } // Update the status field of the matching array element
  //         );
  //       });

  //     }
  //     res.status(201).json(newChallan);
  //   } catch (error) {
  //     res.status(400).json({ message: error.message });
  //   }
  // }

  async createChallan(req, res) {
    try {
      const challanData = req.body;

      // Check if any carpet in the carpetList has a negative area

      const newChallan = await this.createChallanService.createChallan(
        challanData
      );

      if (newChallan) {
        newChallan.carpetList.forEach(async (elem) => {
          console.log(elem.barcodeNo);
          if (elem.area < 0) {
            await ContainerReceived.updateOne(
              { "containerItem.GerCarpetNo": elem.barcodeNo }, // Filter to find the document with GerCarpetNo equal to the barcode number
              { $set: { "containerItem.$.status": "return" } } // Update the status field of the matching array element
            );
          } else {
            await ContainerReceived.updateOne(
              { "containerItem.GerCarpetNo": elem.barcodeNo }, // Filter to find the document with GerCarpetNo equal to the barcode number
              { $set: { "containerItem.$.status": "sale" } } // Update the status field of the matching array element
            );
          }
        });
      }
      if (newChallan) {
      return  res.status(201).json(newChallan);
      }
      return res.status(201).json("already exist");
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  }

  async getAllChallans(req, res) {
    try {
      let filterValue  = req.body;
      const challans = await this.createChallanService.getAllChallans(filterValue);
      res.json(challans);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getChallans(req, res) {
    try {
      let filterValue  = req.body;
      const challans = await this.createChallanService.getChallans(filterValue);
      res.json(challans);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getChallanById(req, res) {
    try {
      const id = req.params.id;
      const challan = await this.createChallanService.getChallanById(id);
      if (!challan) {
        res.status(404).json({ message: "Challan not found" });
      } else {
        res.json(challan);
      }
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async updateChallan(req, res) {
    try {
      const id = req.params.id;
      const challanData = req.body;
      let flag = 0;
      for (const elem of challanData.carpetList) {
        if (elem.id != "undefined") {
          let update = await CreateChallan.updateOne(
            { "carpetList._id": elem.id },
            {
              $set: {
                "carpetList.$.barcodeNo": elem.elem,
                "carpetList.$.amount": elem.amount,
                "carpetList.$.size": elem.size,
                "carpetList.$.area": elem.area,
                "carpetList.$.evkPrice": elem.evkPrice,
              },
            }
          );
          if (update) {
            flag = 1;
          }
        } else {
          let updateAndAdd = await CreateChallan.updateOne(
            { _id: id },
            {
              $push: {
                carpetList: {
                  barcodeNo: elem.barcodeNo,
                  amount: elem.amount,
                  size: elem.size,
                  area: elem.area,
                  evkPrice: elem.evkPrice,
                  status: elem.status,
                },
              },
            }
          );
          if (updateAndAdd) {
            if (elem.area > 0) {
              await ContainerReceived.updateOne(
                { "containerItem.GerCarpetNo": elem.barcodeNo },
                { $set: { "containerItem.$.status": "sale" } }
              );
            } else {
              await ContainerReceived.updateOne(
                { "containerItem.GerCarpetNo": elem.barcodeNo },
                { $set: { "containerItem.$.status": "return" } }
              );
            }

            flag = 1;
          }
        }
      }

      if (id !== null) {
        const updateDocumentFields = await CreateChallan.updateOne(
          { _id: id },
          {
            $set: {
              group: challanData.group,
              wholeseller: challanData.wholeseller,
              retailerOutlet: challanData.retailerOutlet,
              chooseAdate: challanData.chooseAdate,
              RetailerName: challanData.RetailerName,
              challanNo: challanData.challanNo,
              saleRetailer: challanData.saleRetailer,
              total: challanData.total, // Include total
              profit: challanData.profit, // Include profit
              gst: challanData.gst, // Include gst
              discount: challanData.discount, // Include discount
            },
          }
        );
        if (updateDocumentFields.modifiedCount > 0) {
          flag = 1;
        }
      }
      //   const updatedChallan = await this.createChallanService.updateChallan(
      //     id,
      //     challanData
      //   );

      if (flag == 1) {
        return res.status(200).send({
          success: false,
          message: "This challan has been updated",
        });
      }
      res.json(updatedChallan);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async deleteChallanItem(req, res) {
    try {
      const { id, itemId } = req.params;
      const getChallan = await this.createChallanService.getChallanById(id);

      let isExistBill = await BillWholeseller.findOne({
        "challanNo.$.challanNumber": getChallan.challanNo,
      });
      if (isExistBill) {
        return res.status(400).send({
          success: false,
          message: "This challan has been billed",
        });
      }

      if (itemId !== "undefined") {
        const data = await this.createChallanService.getChallanById(id);
        let flag = 0;
        let updated;

        for (const element of data.carpetList) {
          if (element._id == itemId) {
            updated = await CreateChallan.updateOne(
              { "carpetList._id": itemId },
              {
                $set: {
                  "carpetList.$.status": "return",
                  "carpetList.$.isDeleted": true,
                },
              },
              { new: true }
            );

            if (updated.acknowledged === true && updated.matchedCount === 1) {
              let carpetItem = await ContainerReceived.updateOne(
                { "containerItem.GerCarpetNo": element.barcodeNo },
                { $set: { "containerItem.$.status": "return" } }
              );
              if (
                carpetItem.acknowledged === true &&
                carpetItem.matchedCount === 1
              ) {
                flag = 1;
              }
              break; // Break the loop once the update is successful
            }
          }
        }

        if (flag === 1) {
          res.status(200).send({
            success: true,
            message: "Successfully updated challan",
            data: updated,
          });
        } else {
          res.status(200).send({
            success: false,
            message: "Failed! Something went wrong",
          });
        }
      } else {
        const data = await this.createChallanService.getChallanById(id);
        let flag = 0;
        let updated;
        if (data.carpetList.length > 0) {
          for (const element of data.carpetList) {
            if (element._id) {
              updated = await CreateChallan.updateOne(
                { "carpetList._id": element._id },
                {
                  $set: {
                    "carpetList.$.status": "return",
                    "carpetList.$.isDeleted": true,
                  },
                },
                { new: true }
              );

              if (updated.acknowledged === true && updated.matchedCount === 1) {
                let carpetItem = await ContainerReceived.updateOne(
                  { "containerItem.GerCarpetNo": element.barcodeNo },
                  { $set: { "containerItem.$.status": "return" } }
                );
                if (
                  carpetItem.acknowledged === true &&
                  carpetItem.matchedCount === 1
                ) {
                  flag = 1;
                }
              }
            }
          }
        } else {
          flag = 1;
        }
        if (flag === 1) {
          await CreateChallan.deleteOne({ _id: id })
          // .updateOne(
          //   { _id: id },
          //   { $set: { isDeleted: true } }
          // );
          res.status(200).send({
            success: true,
            message: "Successfully delete challan",
            data: updated,
          });
        } else {
          res.status(200).send({
            success: false,
            message: "Failed! Something went wrong",
          });
        }
      }
    } catch (error) {
      console.error(error);
      res.status(500).send({
        success: false,
        message: "Internal Server Error",
      });
    }
  }

  async getAllDeletedChallanHistory(req, res) {
    try {
      const data = await this.createChallanService.getDeletedChllanData();
      //console.log('fgh',data)
      res.status(200).send({ success: true, data: data });
    } catch (error) {
      res.status(400).send({ message: error.message });
    }
  }

  async processReturn(req, res) {
    try {
      const { barcodeNo } = req.body;
      const returnStatus = await this.createChallanService.processReturn(
        barcodeNo
      );
      res.json({ success: true, returned: returnStatus });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async getBarcodeHistory(req, res) {
    try {
      const { barcodeNo } = req.params;
      const history = await this.createChallanService.getBarcodeHistory(
        barcodeNo
      );
      res.json(history);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = CreateChallanController;
