const mapMasterService = require('../services/mapMaster-service');
const path = require('path'); // Import the 'path' module

const createMapMaster = async (req, res) => {
    try {
        const { quality, addDesign, color, groundColour, borderColour, sizeMaster } = req.body;
        let fileName = '';
        if (req.files && req.files.length > 0) {
          fileName = req.files[0].filename; // Get the filename of the uploaded file from req.files
        }
        const newMapMaster = await mapMasterService.create({
            quality,
            addDesign,
            color,
            groundColour,
            borderColour,
            sizeMaster,
            uploadedFile: fileName // Store the URL path for the file
        });

        res.status(201).json(newMapMaster);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getAllMapMasters = async (req, res) => {
    try {
        const mapMasters = await mapMasterService.findAll();
        res.status(200).json(mapMasters);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getMapMasterById = async (req, res) => {
    try {
        const mapMaster = await mapMasterService.findById(req.params.id);
        if (!mapMaster) {
            return res.status(404).json({ message: "MapMaster not found" });
        }
        res.status(200).json(mapMaster);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const updateMapMaster = async (req, res) => {
  try {
      const { quality, addDesign, color, groundColour, borderColour, sizeMaster } = req.body;
      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename; // Get the filename of the uploaded file from req.files
      }

      const updatedMapMasterData = {
          quality,
          addDesign,
          color,
          groundColour,
          borderColour,
          sizeMaster,
          uploadedFile: fileName // Use uploadedFile variable here
      };

      const updatedMapMaster = await mapMasterService.update(req.params.id, updatedMapMasterData);

      if (!updatedMapMaster) {
          return res.status(404).json({ message: "MapMaster not found" });
      }

      res.status(200).json(updatedMapMaster);
  } catch (error) {
      res.status(500).json({ message: error.message });
  }
};

const deleteMapMaster = async (req, res) => {
    try {
        const deletedMapMaster = await mapMasterService.delete(req.params.id);
        if (!deletedMapMaster) {
            return res.status(404).json({ message: "MapMaster not found" });
        }
        res.status(200).json({ message: "MapMaster deleted successfully" });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    createMapMaster,
    getAllMapMasters,
    getMapMasterById,
    updateMapMaster,
    deleteMapMaster
};
