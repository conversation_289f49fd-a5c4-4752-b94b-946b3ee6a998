const mongoose=require('mongoose');

const connect=async()=>{
    try {
        // Connect to MongoDB Atlas with increased timeout
        await mongoose.connect("mongodb+srv://infosarthaktech:<EMAIL>/test", {
            serverSelectionTimeoutMS: 30000, // 30 seconds timeout
            socketTimeoutMS: 45000, // 45 seconds timeout
            connectTimeoutMS: 30000 // 30 seconds connection timeout
        });
        console.log("Connected to MongoDB Atlas");
    } catch (error) {
        console.error("Failed to connect to MongoDB:", error);
        throw error;
    }
};

module.exports=connect;