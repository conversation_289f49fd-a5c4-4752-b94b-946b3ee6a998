const sizeService = require('../services/size-service');

class SizeController {
  async createSize(req, res) {
    try {
      const {size,sizeCode,sequenceCode,khapSize,orderSize,unit} = req.body;
      const sizeDoc = await sizeService.createSize({size,sizeCode,sequenceCode,khapSize,orderSize,unit});
      res.status(201).json(sizeDoc);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getSizeById(req, res) {
    try {
      const sizeId = req.params.id;
      const size = await sizeService.getSizeById(sizeId);
      res.status(200).json(size);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllSizes(req, res) {
    try {
      const sizes = await sizeService.getAllSizes();
      res.status(200).json(sizes);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateSize(req, res) {
    try {
      const sizeId = req.params.id;
      const sizeData = req.body;
      const updatedSize = await sizeService.updateSize(sizeId, sizeData);
      res.status(200).json(updatedSize);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteSize(req, res) {
    try {
      const sizeId = req.params.id;
      const deletedSize = await sizeService.deleteSize(sizeId);
      res.status(200).json(deletedSize);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new SizeController();
