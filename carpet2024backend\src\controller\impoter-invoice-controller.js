// // impoterController.js
// const impoterService = require('../services/impoter-invoice-service');

// async function createImpoterDetail(req, res) {
//     try {
         
//         const data = req.body;
//         const newImpoterDetail = await impoterService.createImpoterDetail(data);
//         res.status(201).json(newImpoterDetail);
//     } catch (error) {
//         res.status(400).json({ message: error.message });
//     }
// }

// async function getImpoterDetailById(req, res) {
//     try {
//         const { id } = req.params;
//         const impoterDetail = await impoterService.getImpoterDetailById(id);
//         if (!impoterDetail) {
//             return res.status(404).json({ message: 'Impoter detail not found' });
//         }
//         res.json(impoterDetail);
//     } catch (error) {
//         res.status(500).json({ message: 'Internal Server Error' });
//     }
// }
// async function getAllImpoterDetails(req, res) {
//     try {
//         const allImpoterDetails = await impoterService.getAllImpoterDetails();
//         res.json(allImpoterDetails);
//     } catch (error) {
//         res.status(500).json({ message: 'Internal Server Error' });
//     }
// }
// async function updateImpoterDetail(req, res) {
//     try {
//         const { id } = req.params;
//         const data = req.body;
//         const updatedImpoterDetail = await impoterService.updateImpoterDetail(id, data);
//         res.json(updatedImpoterDetail);
//     } catch (error) {
//         res.status(400).json({ message: error.message });
//     }
// }

// async function deleteImpoterDetail(req, res) {
//     try {
//         const { id } = req.params;
//         await impoterService.deleteImpoterDetail(id);
//         res.sendStatus(204);
//     } catch (error) {
//         res.status(500).json({ message: 'Internal Server Error' });
//     }
// }

// module.exports = {
//     createImpoterDetail,
//     getImpoterDetailById,
//     getAllImpoterDetails,
//     updateImpoterDetail,
//     deleteImpoterDetail
// };

const impoterService = require('../services/impoter-invoice-service');


async function createImpoterDetail(req, res) {
    try {
        const data = req.body;
        const newImpoterDetail = await impoterService.createImpoterDetail(data);
        res.status(201).json(newImpoterDetail);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

async function getImpoterDetailById(req, res) {
    try {
        const { id } = req.params;
        const impoterDetail = await impoterService.getImpoterDetailById(id);
        if (!impoterDetail) {
            return res.status(404).json({ message: 'Impoter detail not found' });
        }
        res.json(impoterDetail);
    } catch (error) {
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

async function getAllImpoterDetails(req, res) {
    try {
        const allImpoterDetails = await impoterService.getAllImpoterDetails();
        res.json(allImpoterDetails);
    } catch (error) {
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

async function updateImpoterDetail(req, res) {
    try {
        const { id } = req.params;
        const data = req.body;
        const updatedImpoterDetail = await impoterService.updateImpoterDetail(id, data);
        res.json(updatedImpoterDetail);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

async function deleteImpoterDetail(req, res) {
    try {
        const { id } = req.params;
        await impoterService.deleteImpoterDetail(id);
        res.sendStatus(204);
    } catch (error) {
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

module.exports = {
    createImpoterDetail,
    getImpoterDetailById,
    getAllImpoterDetails,
    updateImpoterDetail,
    deleteImpoterDetail
};
