const billOfLadingService = require('../services/billOfLading-service');

class BillOfLadingController {
  async getAllBillOfLadings(req, res) {
    try {
      const allBillOfLadings = await billOfLadingService.getAllBillOfLadings();
      res.status(200).json(allBillOfLadings);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getBillOfLadingById(req, res) {
    try {
      const billOfLadingId = req.params.id;
      const billOfLading = await billOfLadingService.getBillOfLadingById(billOfLadingId);
      res.status(200).json(billOfLading);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async createBillOfLading(req, res) {
    try {

      const pdfData = req.file;
      const billOfLading = await billOfLadingService.createBillOfLading(pdfData);
      res.status(201).json(billOfLading);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateBillOfLading(req, res) {
    try {
      const billOfLadingId = req.params.id;
      const updatedData = req.body;
      const updatedBillOfLading = await billOfLadingService.updateBillOfLading(billOfLadingId, updatedData);
      res.status(200).json(updatedBillOfLading);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteBillOfLading(req, res) {
    try {
      const billOfLadingId = req.params.id;
      const deletedBillOfLading = await billOfLadingService.deleteBillOfLading(billOfLadingId);
      res.status(200).json(deletedBillOfLading);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new BillOfLadingController();
