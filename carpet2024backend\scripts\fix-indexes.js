const mongoose = require('mongoose');

async function fixIndexes() {
    try {
        await mongoose.connect('mongodb://localhost:27017/test');
        console.log('Connected to MongoDB');
        
        // Drop all indexes from carpetreceiveds collection
        await mongoose.connection.collection('carpetreceiveds').dropIndexes();
        console.log('Dropped all indexes');
        
        // Create new non-unique index on issueNo fields
        await mongoose.connection.collection('carpetreceiveds').createIndex(
            { 
                'issueNo._id': 1,
                'issueNo.Br_issueNo': 1
            },
            { unique: false }
        );
        console.log('Created new non-unique index');
        
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

fixIndexes();
