(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ca(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var l=ca(this);function p(a,b){if(b)a:{var c=l;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}
p("Symbol",function(a){function b(h){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(h||"")+"_"+e++,h)}function c(h,f){this.g=h;ba(this,"description",{configurable:!0,writable:!0,value:f})}if(a)return a;c.prototype.toString=function(){return this.g};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0;return b});
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=l[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return da(aa(this))}})}return a});p("Symbol.asyncIterator",function(a){return a?a:Symbol("Symbol.asyncIterator")});
function da(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}function q(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}function ea(a){if(!(a instanceof Array)){a=q(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function r(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
var fa="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)r(d,e)&&(a[e]=d[e])}return a};p("Object.assign",function(a){return a||fa});var ha;if("function"==typeof Object.setPrototypeOf)ha=Object.setPrototypeOf;else{var ia;a:{var ja={a:!0},ka={};try{ka.__proto__=ja;ia=ka.a;break a}catch(a){}ia=!1}ha=ia?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var t=ha;
function la(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}
p("Promise",function(a){function b(f){this.g=0;this.i=void 0;this.h=[];this.o=!1;var g=this.j();try{f(g.resolve,g.reject)}catch(k){g.reject(k)}}function c(){this.g=null}function d(f){return f instanceof b?f:new b(function(g){g(f)})}if(a)return a;c.prototype.h=function(f){if(null==this.g){this.g=[];var g=this;this.i(function(){g.l()})}this.g.push(f)};var e=l.setTimeout;c.prototype.i=function(f){e(f,0)};c.prototype.l=function(){for(;this.g&&this.g.length;){var f=this.g;this.g=[];for(var g=0;g<f.length;++g){var k=
f[g];f[g]=null;try{k()}catch(m){this.j(m)}}}this.g=null};c.prototype.j=function(f){this.i(function(){throw f;})};b.prototype.j=function(){function f(m){return function(n){k||(k=!0,m.call(g,n))}}var g=this,k=!1;return{resolve:f(this.D),reject:f(this.l)}};b.prototype.D=function(f){if(f===this)this.l(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof b)this.O(f);else{a:switch(typeof f){case "object":var g=null!=f;break a;case "function":g=!0;break a;default:g=!1}g?this.C(f):this.m(f)}};
b.prototype.C=function(f){var g=void 0;try{g=f.then}catch(k){this.l(k);return}"function"==typeof g?this.P(g,f):this.m(f)};b.prototype.l=function(f){this.u(2,f)};b.prototype.m=function(f){this.u(1,f)};b.prototype.u=function(f,g){if(0!=this.g)throw Error("Cannot settle("+f+", "+g+"): Promise already settled in state"+this.g);this.g=f;this.i=g;2===this.g&&this.G();this.A()};b.prototype.G=function(){var f=this;e(function(){if(f.B()){var g=l.console;"undefined"!==typeof g&&g.error(f.i)}},1)};b.prototype.B=
function(){if(this.o)return!1;var f=l.CustomEvent,g=l.Event,k=l.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof f?f=new f("unhandledrejection",{cancelable:!0}):"function"===typeof g?f=new g("unhandledrejection",{cancelable:!0}):(f=l.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.i;return k(f)};b.prototype.A=function(){if(null!=this.h){for(var f=0;f<this.h.length;++f)h.h(this.h[f]);this.h=null}};var h=new c;b.prototype.O=
function(f){var g=this.j();f.F(g.resolve,g.reject)};b.prototype.P=function(f,g){var k=this.j();try{f.call(g,k.resolve,k.reject)}catch(m){k.reject(m)}};b.prototype.then=function(f,g){function k(y,G){return"function"==typeof y?function(ra){try{m(y(ra))}catch(sa){n(sa)}}:G}var m,n,u=new b(function(y,G){m=y;n=G});this.F(k(f,m),k(g,n));return u};b.prototype.catch=function(f){return this.then(void 0,f)};b.prototype.F=function(f,g){function k(){switch(m.g){case 1:f(m.i);break;case 2:g(m.i);break;default:throw Error("Unexpected state: "+
m.g);}}var m=this;null==this.h?h.h(k):this.h.push(k);this.o=!0};b.resolve=d;b.reject=function(f){return new b(function(g,k){k(f)})};b.race=function(f){return new b(function(g,k){for(var m=q(f),n=m.next();!n.done;n=m.next())d(n.value).F(g,k)})};b.all=function(f){var g=q(f),k=g.next();return k.done?d([]):new b(function(m,n){function u(ra){return function(sa){y[ra]=sa;G--;0==G&&m(y)}}var y=[],G=0;do y.push(void 0),G++,d(k.value).F(u(y.length-1),n),k=g.next();while(!k.done)})};return b});
p("Object.setPrototypeOf",function(a){return a||t});
p("WeakMap",function(a){function b(k){this.g=(g+=Math.random()+1).toString();if(k){k=q(k);for(var m;!(m=k.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(k){var m=typeof k;return"object"===m&&null!==k||"function"===m}function e(k){if(!r(k,f)){var m=new c;ba(k,f,{value:m})}}function h(k){var m=Object[k];m&&(Object[k]=function(n){if(n instanceof c)return n;Object.isExtensible(n)&&e(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),m=Object.seal({}),
n=new a([[k,2],[m,3]]);if(2!=n.get(k)||3!=n.get(m))return!1;n.delete(k);n.set(m,4);return!n.has(k)&&4==n.get(m)}catch(u){return!1}}())return a;var f="$jscomp_hidden_"+Math.random();h("freeze");h("preventExtensions");h("seal");var g=0;b.prototype.set=function(k,m){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!r(k,f))throw Error("WeakMap key fail: "+k);k[f][this.g]=m;return this};b.prototype.get=function(k){return d(k)&&r(k,f)?k[f][this.g]:void 0};b.prototype.has=function(k){return d(k)&&r(k,
f)&&r(k[f],this.g)};b.prototype.delete=function(k){return d(k)&&r(k,f)&&r(k[f],this.g)?delete k[f][this.g]:!1};return b});
p("Map",function(a){function b(){var g={};return g.v=g.next=g.head=g}function c(g,k){var m=g.g;return da(function(){if(m){for(;m.head!=g.g;)m=m.v;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})}function d(g,k){var m=k&&typeof k;"object"==m||"function"==m?h.has(k)?m=h.get(k):(m=""+ ++f,h.set(k,m)):m="p_"+k;var n=g.h[m];if(n&&r(g.h,m))for(g=0;g<n.length;g++){var u=n[g];if(k!==k&&u.key!==u.key||k===u.key)return{id:m,list:n,index:g,s:u}}return{id:m,list:n,
index:-1,s:void 0}}function e(g){this.h={};this.g=b();this.size=0;if(g){g=q(g);for(var k;!(k=g.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var g=Object.seal({x:4}),k=new a(q([[g,"s"]]));if("s"!=k.get(g)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var m=k.entries(),n=m.next();if(n.done||n.value[0]!=g||"s"!=n.value[1])return!1;n=m.next();return n.done||4!=n.value[0].x||
"t"!=n.value[1]||!m.next().done?!1:!0}catch(u){return!1}}())return a;var h=new WeakMap;e.prototype.set=function(g,k){g=0===g?0:g;var m=d(this,g);m.list||(m.list=this.h[m.id]=[]);m.s?m.s.value=k:(m.s={next:this.g,v:this.g.v,head:this.g,key:g,value:k},m.list.push(m.s),this.g.v.next=m.s,this.g.v=m.s,this.size++);return this};e.prototype.delete=function(g){g=d(this,g);return g.s&&g.list?(g.list.splice(g.index,1),g.list.length||delete this.h[g.id],g.s.v.next=g.s.next,g.s.next.v=g.s.v,g.s.head=null,this.size--,
!0):!1};e.prototype.clear=function(){this.h={};this.g=this.g.v=b();this.size=0};e.prototype.has=function(g){return!!d(this,g).s};e.prototype.get=function(g){return(g=d(this,g).s)&&g.value};e.prototype.entries=function(){return c(this,function(g){return[g.key,g.value]})};e.prototype.keys=function(){return c(this,function(g){return g.key})};e.prototype.values=function(){return c(this,function(g){return g.value})};e.prototype.forEach=function(g,k){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,
g.call(k,n[1],n[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var f=0;return e});function ma(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var h=c++;return{value:b(h,a[h]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}p("Array.prototype.values",function(a){return a?a:function(){return ma(this,function(b,c){return c})}});p("Array.prototype.keys",function(a){return a?a:function(){return ma(this,function(b){return b})}});
function v(a,b,c){if(null==a)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""}p("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=v(this,b,"endsWith");void 0===c&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;0<e&&0<c;)if(d[--c]!=b[--e])return!1;return 0>=e}});
function na(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var h=a[e];if(b.call(c,h,e,a))return{J:e,N:h}}return{J:-1,N:void 0}}p("Array.prototype.find",function(a){return a?a:function(b,c){return na(this,b,c).N}});p("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=v(this,b,"startsWith"),e=d.length,h=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var f=0;f<h&&c<e;)if(d[c++]!=b[f++])return!1;return f>=h}});
p("Number.isFinite",function(a){return a?a:function(b){return"number"!==typeof b?!1:!isNaN(b)&&Infinity!==b&&-Infinity!==b}});p("String.prototype.repeat",function(a){return a?a:function(b){var c=v(this,null,"repeat");if(0>b||1342177279<b)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}});
p("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(g){return g};var e=[],h="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof h){b=h.call(b);for(var f=0;!(h=b.next()).done;)e.push(c.call(d,h.value,f++))}else for(h=b.length,f=0;f<h;f++)e.push(c.call(d,b[f],f));return e}});p("String.prototype.trimLeft",function(a){function b(){return this.replace(/^[\s\xa0]+/,"")}return a||b});p("String.prototype.trimStart",function(a){return a||String.prototype.trimLeft});
p("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});p("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var h=d[c];if(h===b||Object.is(h,b))return!0}return!1}});p("String.prototype.includes",function(a){return a?a:function(b,c){return-1!==v(this,b,"includes").indexOf(b,c||0)}});
p("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||Infinity===b||-Infinity===b||0===b)return b;var c=Math.floor(Math.abs(b));return 0>b?-c:c}});function oa(a){a=Math.trunc(a)||0;0>a&&(a+=this.length);if(!(0>a||a>=this.length))return this[a]}p("Array.prototype.at",function(a){return a?a:oa});
p("Array.prototype.copyWithin",function(a){function b(c){c=Number(c);return Infinity===c||-Infinity===c?c:c|0}return a?a:function(c,d,e){var h=this.length;c=b(c);d=b(d);e=void 0===e?h:b(e);c=0>c?Math.max(h+c,0):Math.min(c,h);d=0>d?Math.max(h+d,0):Math.min(d,h);e=0>e?Math.max(h+e,0):Math.min(e,h);if(c<d)for(;d<e;)d in this?this[c++]=this[d++]:(delete this[c++],d++);else for(e=Math.min(e,h+d-c),c+=e-d;e>d;)--e in this?this[--c]=this[e]:delete this[--c];return this}});
p("Array.prototype.entries",function(a){return a?a:function(){return ma(this,function(b,c){return[b,c]})}});p("Array.prototype.fill",function(a){return a?a:function(b,c,d){var e=this.length||0;0>c&&(c=Math.max(0,e+c));if(null==d||d>e)d=e;d=Number(d);0>d&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});p("Array.prototype.findIndex",function(a){return a?a:function(b,c){return na(this,b,c).J}});
p("Array.prototype.flat",function(a){return a?a:function(b){b=void 0===b?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&0<b?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});p("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,h){e=b.call(c,e,h,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});p("Array.of",function(a){return a?a:function(b){return Array.from(arguments)}});
p("globalThis",function(a){return a||l});p("Math.acosh",function(a){return a?a:function(b){b=Number(b);return Math.log(b+Math.sqrt(b*b-1))}});p("Math.asinh",function(a){return a?a:function(b){b=Number(b);if(0===b)return b;var c=Math.log(Math.abs(b)+Math.sqrt(b*b+1));return 0>b?-c:c}});p("Math.log1p",function(a){return a?a:function(b){b=Number(b);if(.25>b&&-.25<b){for(var c=b,d=1,e=b,h=0,f=1;h!=e;)c*=b,f*=-1,e=(h=e)+f*c/++d;return e}return Math.log(1+b)}});
p("Math.atanh",function(a){if(a)return a;var b=Math.log1p;return function(c){c=Number(c);return(b(c)-b(-c))/2}});p("Math.cbrt",function(a){return a?a:function(b){if(0===b)return b;b=Number(b);var c=Math.pow(Math.abs(b),1/3);return 0>b?-c:c}});p("Math.clz32",function(a){return a?a:function(b){b=Number(b)>>>0;if(0===b)return 32;var c=0;0===(b&4294901760)&&(b<<=16,c+=16);0===(b&4278190080)&&(b<<=8,c+=8);0===(b&4026531840)&&(b<<=4,c+=4);0===(b&3221225472)&&(b<<=2,c+=2);0===(b&2147483648)&&c++;return c}});
p("Math.cosh",function(a){if(a)return a;var b=Math.exp;return function(c){c=Number(c);return(b(c)+b(-c))/2}});p("Math.expm1",function(a){return a?a:function(b){b=Number(b);if(.25>b&&-.25<b){for(var c=b,d=1,e=b,h=0;h!=e;)c*=b/++d,e=(h=e)+c;return e}return Math.exp(b)-1}});p("Math.fround",function(a){if(a)return a;if("function"!==typeof Float32Array)return function(c){return c};var b=new Float32Array(1);return function(c){b[0]=c;return b[0]}});
p("Math.hypot",function(a){return a?a:function(b){if(2>arguments.length)return arguments.length?Math.abs(arguments[0]):0;var c,d,e;for(c=e=0;c<arguments.length;c++)e=Math.max(e,Math.abs(arguments[c]));if(1E100<e||1E-100>e){if(!e)return e;for(c=d=0;c<arguments.length;c++){var h=Number(arguments[c])/e;d+=h*h}return Math.sqrt(d)*e}for(c=d=0;c<arguments.length;c++)h=Number(arguments[c]),d+=h*h;return Math.sqrt(d)}});
p("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});p("Math.log10",function(a){return a?a:function(b){return Math.log(b)/Math.LN10}});p("Math.log2",function(a){return a?a:function(b){return Math.log(b)/Math.LN2}});p("Math.sign",function(a){return a?a:function(b){b=Number(b);return 0===b||isNaN(b)?b:0<b?1:-1}});
p("Math.sinh",function(a){if(a)return a;var b=Math.exp;return function(c){c=Number(c);return 0===c?c:(b(c)-b(-c))/2}});p("Math.tanh",function(a){return a?a:function(b){b=Number(b);if(0===b)return b;var c=Math.exp(-2*Math.abs(b));c=(1-c)/(1+c);return 0>b?-c:c}});p("Number.EPSILON",function(){return Math.pow(2,-52)});p("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});p("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});
p("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});p("Number.isNaN",function(a){return a?a:function(b){return"number"===typeof b&&isNaN(b)}});p("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});p("Number.parseFloat",function(a){return a||parseFloat});p("Number.parseInt",function(a){return a||parseInt});
p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)r(b,d)&&c.push([d,b[d]]);return c}});p("Object.fromEntries",function(a){return a?a:function(b){var c={};if(!(Symbol.iterator in b))throw new TypeError(""+b+" is not iterable");b=b[Symbol.iterator].call(b);for(var d=b.next();!d.done;d=b.next()){d=d.value;if(Object(d)!==d)throw new TypeError("iterable for fromEntries should yield objects");c[d[0]]=d[1]}return c}});p("Reflect",function(a){return a?a:{}});
p("Object.getOwnPropertySymbols",function(a){return a?a:function(){return[]}});p("Reflect.ownKeys",function(a){return a?a:function(b){var c=[],d=Object.getOwnPropertyNames(b);b=Object.getOwnPropertySymbols(b);for(var e=0;e<d.length;e++)("jscomp_symbol_"==d[e].substring(0,14)?b:c).push(d[e]);return c.concat(b)}});p("Object.getOwnPropertyDescriptors",function(a){return a?a:function(b){for(var c={},d=Reflect.ownKeys(b),e=0;e<d.length;e++)c[d[e]]=Object.getOwnPropertyDescriptor(b,d[e]);return c}});
p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)r(b,d)&&c.push(b[d]);return c}});p("Object.hasOwn",function(a){return a?a:function(b,c){return Object.prototype.hasOwnProperty.call(b,c)}});p("Promise.allSettled",function(a){function b(d){return{status:"fulfilled",value:d}}function c(d){return{status:"rejected",reason:d}}return a?a:function(d){var e=this;d=Array.from(d,function(h){return e.resolve(h).then(b,c)});return e.all(d)}});
p("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});var pa="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b};
function qa(a,b){a.prototype=pa(b.prototype);a.prototype.constructor=a;if(t)t(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.T=b.prototype}p("AggregateError",function(a){function b(c,d){d=Error(d);"stack"in d&&(this.stack=d.stack);this.errors=c;this.message=d.message}if(a)return a;qa(b,Error);b.prototype.name="AggregateError";return b});
p("Promise.any",function(a){return a?a:function(b){b=b instanceof Array?b:Array.from(b);return Promise.all(b.map(function(c){return Promise.resolve(c).then(function(d){throw d;},function(d){return d})})).then(function(c){throw new AggregateError(c,"All promises were rejected");},function(c){return c})}});p("Reflect.apply",function(a){if(a)return a;var b=Function.prototype.apply;return function(c,d,e){return b.call(c,d,e)}});
var ta=function(){function a(){function c(){}new c;Reflect.construct(c,[],function(){});return new c instanceof c}if("undefined"!=typeof Reflect&&Reflect.construct){if(a())return Reflect.construct;var b=Reflect.construct;return function(c,d,e){c=b(c,d);e&&Reflect.setPrototypeOf(c,e.prototype);return c}}return function(c,d,e){void 0===e&&(e=c);e=pa(e.prototype||Object.prototype);return Function.prototype.apply.call(c,e,d)||e}}();p("Reflect.construct",function(){return ta});
p("Reflect.defineProperty",function(a){return a?a:function(b,c,d){try{Object.defineProperty(b,c,d);var e=Object.getOwnPropertyDescriptor(b,c);return e?e.configurable===(d.configurable||!1)&&e.enumerable===(d.enumerable||!1)&&("value"in e?e.value===d.value&&e.writable===(d.writable||!1):e.get===d.get&&e.set===d.set):!1}catch(h){return!1}}});p("Reflect.deleteProperty",function(a){return a?a:function(b,c){if(!r(b,c))return!0;try{return delete b[c]}catch(d){return!1}}});
p("Reflect.getOwnPropertyDescriptor",function(a){return a||Object.getOwnPropertyDescriptor});p("Reflect.getPrototypeOf",function(a){return a||Object.getPrototypeOf});function ua(a,b){for(;a;){var c=Reflect.getOwnPropertyDescriptor(a,b);if(c)return c;a=Reflect.getPrototypeOf(a)}}p("Reflect.get",function(a){return a?a:function(b,c,d){if(2>=arguments.length)return b[c];var e=ua(b,c);if(e)return e.get?e.get.call(d):e.value}});p("Reflect.has",function(a){return a?a:function(b,c){return c in b}});
p("Reflect.isExtensible",function(a){return a?a:"function"==typeof Object.isExtensible?Object.isExtensible:function(){return!0}});p("Reflect.preventExtensions",function(a){return a?a:"function"!=typeof Object.preventExtensions?function(){return!1}:function(b){Object.preventExtensions(b);return!Object.isExtensible(b)}});
p("Reflect.set",function(a){return a?a:function(b,c,d,e){var h=ua(b,c);return h?h.set?(h.set.call(3<arguments.length?e:b,d),!0):h.writable&&!Object.isFrozen(b)?(b[c]=d,!0):!1:Reflect.isExtensible(b)?(b[c]=d,!0):!1}});p("Reflect.setPrototypeOf",function(a){return a?a:t?function(b,c){try{return t(b,c),!0}catch(d){return!1}}:null});
p("Set",function(a){function b(c){this.g=new Map;if(c){c=q(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(q([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),h=e.next();if(h.done||h.value[0]!=c||h.value[1]!=c)return!1;h=e.next();return h.done||h.value[0]==c||4!=h.value[0].x||
h.value[1]!=h.value[0]?!1:e.next().done}catch(f){return!1}}())return a;b.prototype.add=function(c){c=0===c?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(h){return c.call(d,h,h,e)})};return b});p("String.prototype.at",function(a){return a?a:oa});p("String.prototype.codePointAt",function(a){return a?a:function(b){var c=v(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(0<=b&&b<d){b|=0;var e=c.charCodeAt(b);if(55296>e||56319<e||b+1===d)return e;b=c.charCodeAt(b+1);return 56320>b||57343<b?e:1024*(e-55296)+b+9216}}});
p("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(0>e||1114111<e||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);65535>=e?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});
p("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,h={next:function(){if(e)return{value:void 0,done:!0};var f=c.exec(d);if(!f)return e=!0,{value:void 0,done:!0};""===f[0]&&(c.lastIndex+=1);return{value:f,done:!1}}};h[Symbol.iterator]=function(){return h};return h}});
function va(a,b){a=void 0!==a?String(a):" ";return 0<b&&a?a.repeat(Math.ceil(b/a.length)).substring(0,b):""}p("String.prototype.padEnd",function(a){return a?a:function(b,c){var d=v(this,null,"padStart");return d+va(c,b-d.length)}});p("String.prototype.padStart",function(a){return a?a:function(b,c){var d=v(this,null,"padStart");return va(c,b-d.length)+d}});
p("String.raw",function(a){return a?a:function(b,c){if(null==b)throw new TypeError("Cannot convert undefined or null to object");for(var d=b.raw,e=d.length,h="",f=0;f<e;++f)h+=d[f],f+1<e&&f+1<arguments.length&&(h+=String(arguments[f+1]));return h}});
p("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});p("String.prototype.trimRight",function(a){function b(){return this.replace(/[\s\xa0]+$/,"")}return a||b});
p("String.prototype.trimEnd",function(a){return a||String.prototype.trimRight});function w(a){return a?a:oa}p("Int8Array.prototype.at",w);p("Uint8Array.prototype.at",w);p("Uint8ClampedArray.prototype.at",w);p("Int16Array.prototype.at",w);p("Uint16Array.prototype.at",w);p("Int32Array.prototype.at",w);p("Uint32Array.prototype.at",w);p("Float32Array.prototype.at",w);p("Float64Array.prototype.at",w);function x(a){return a?a:Array.prototype.copyWithin}p("Int8Array.prototype.copyWithin",x);
p("Uint8Array.prototype.copyWithin",x);p("Uint8ClampedArray.prototype.copyWithin",x);p("Int16Array.prototype.copyWithin",x);p("Uint16Array.prototype.copyWithin",x);p("Int32Array.prototype.copyWithin",x);p("Uint32Array.prototype.copyWithin",x);p("Float32Array.prototype.copyWithin",x);p("Float64Array.prototype.copyWithin",x);function z(a){return a?a:Array.prototype.fill}p("Int8Array.prototype.fill",z);p("Uint8Array.prototype.fill",z);p("Uint8ClampedArray.prototype.fill",z);
p("Int16Array.prototype.fill",z);p("Uint16Array.prototype.fill",z);p("Int32Array.prototype.fill",z);p("Uint32Array.prototype.fill",z);p("Float32Array.prototype.fill",z);p("Float64Array.prototype.fill",z);
p("WeakSet",function(a){function b(c){this.g=new WeakMap;if(c){c=q(c);for(var d;!(d=c.next()).done;)this.add(d.value)}}if(function(){if(!a||!Object.seal)return!1;try{var c=Object.seal({}),d=Object.seal({}),e=new a([c]);if(!e.has(c)||e.has(d))return!1;e.delete(c);e.add(d);return!e.has(c)&&e.has(d)}catch(h){return!1}}())return a;b.prototype.add=function(c){this.g.set(c,!0);return this};b.prototype.has=function(c){return this.g.has(c)};b.prototype.delete=function(c){return this.g.delete(c)};return b});
var A=this||self;function B(a){a=a.split(".");for(var b=A,c=0;c<a.length;c++)if(b=b[a[c]],null==b)return null;return b}function wa(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function xa(a,b,c){return a.call.apply(a.bind,arguments)}
function ya(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function C(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?C=xa:C=ya;return C.apply(null,arguments)}
function D(a,b){a=a.split(".");var c=A;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}function E(a,b){function c(){}c.prototype=b.prototype;a.T=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.U=function(d,e,h){for(var f=Array(arguments.length-2),g=2;g<arguments.length;g++)f[g-2]=arguments[g];return b.prototype[e].apply(d,f)}}function za(a){return a};function F(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,F);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}E(F,Error);F.prototype.name="CustomError";function H(a,b){this.g=a===Aa&&b||"";this.h=Ba}H.prototype.K=!0;H.prototype.I=function(){return this.g};function Ca(a){return a instanceof H&&a.constructor===H&&a.h===Ba?a.g:"type_error:Const"}function I(a){return new H(Aa,a)}var Ba={},Aa={};var Da={"gstatic.com":{loader:I("https://www.gstatic.com/charts/%{version}/loader.js"),debug:I("https://www.gstatic.com/charts/debug/%{version}/js/jsapi_debug_%{package}_module.js"),debug_i18n:I("https://www.gstatic.com/charts/debug/%{version}/i18n/jsapi_debug_i18n_%{package}_module__%{language}.js"),compiled:I("https://www.gstatic.com/charts/%{version}/js/jsapi_compiled_%{package}_module.js"),compiled_i18n:I("https://www.gstatic.com/charts/%{version}/i18n/jsapi_compiled_i18n_%{package}_module__%{language}.js"),
css:I("https://www.gstatic.com/charts/%{version}/css/%{subdir}/%{filename}"),css2:I("https://www.gstatic.com/charts/%{version}/css/%{subdir1}/%{subdir2}/%{filename}"),third_party:I("https://www.gstatic.com/charts/%{version}/third_party/%{subdir}/%{filename}"),third_party2:I("https://www.gstatic.com/charts/%{version}/third_party/%{subdir1}/%{subdir2}/%{filename}"),third_party_gen:I("https://www.gstatic.com/charts/%{version}/third_party/%{subdir}/%{filename}")},"gstatic.cn":{loader:I("https://www.gstatic.cn/charts/%{version}/loader.js"),
debug:I("https://www.gstatic.cn/charts/debug/%{version}/js/jsapi_debug_%{package}_module.js"),debug_i18n:I("https://www.gstatic.cn/charts/debug/%{version}/i18n/jsapi_debug_i18n_%{package}_module__%{language}.js"),compiled:I("https://www.gstatic.cn/charts/%{version}/js/jsapi_compiled_%{package}_module.js"),compiled_i18n:I("https://www.gstatic.cn/charts/%{version}/i18n/jsapi_compiled_i18n_%{package}_module__%{language}.js"),css:I("https://www.gstatic.cn/charts/%{version}/css/%{subdir}/%{filename}"),
css2:I("https://www.gstatic.cn/charts/%{version}/css/%{subdir1}/%{subdir2}/%{filename}"),third_party:I("https://www.gstatic.cn/charts/%{version}/third_party/%{subdir}/%{filename}"),third_party2:I("https://www.gstatic.cn/charts/%{version}/third_party/%{subdir1}/%{subdir2}/%{filename}"),third_party_gen:I("https://www.gstatic.cn/charts/%{version}/third_party/%{subdir}/%{filename}")}},Ea=["default"];var Fa={"chrome-frame":{versions:{"1.0.0":{uncompressed:"CFInstall.js",compressed:"CFInstall.min.js"},"1.0.1":{uncompressed:"CFInstall.js",compressed:"CFInstall.min.js"},"1.0.2":{uncompressed:"CFInstall.js",compressed:"CFInstall.min.js"}},aliases:{1:"1.0.2","1.0":"1.0.2"}},swfobject:{versions:{"2.1":{uncompressed:"swfobject_src.js",compressed:"swfobject.js"},"2.2":{uncompressed:"swfobject_src.js",compressed:"swfobject.js"}},aliases:{2:"2.2"}},"ext-core":{versions:{"3.1.0":{uncompressed:"ext-core-debug.js",
compressed:"ext-core.js"},"3.0.0":{uncompressed:"ext-core-debug.js",compressed:"ext-core.js"}},aliases:{3:"3.1.0","3.0":"3.0.0","3.1":"3.1.0"}},scriptaculous:{versions:{"1.8.3":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"},"1.9.0":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"},"1.8.1":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"},"1.8.2":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"}},aliases:{1:"1.9.0","1.8":"1.8.3","1.9":"1.9.0"}},
webfont:{versions:{"1.0.12":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.13":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.14":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.15":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.10":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.11":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.27":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.28":{uncompressed:"webfont_debug.js",
compressed:"webfont.js"},"1.0.29":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.23":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.24":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.25":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.26":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.21":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.22":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.3":{uncompressed:"webfont_debug.js",
compressed:"webfont.js"},"1.0.4":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.5":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.6":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.9":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.16":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.17":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.0":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.18":{uncompressed:"webfont_debug.js",
compressed:"webfont.js"},"1.0.1":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.19":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.2":{uncompressed:"webfont_debug.js",compressed:"webfont.js"}},aliases:{1:"1.0.29","1.0":"1.0.29"}},jqueryui:{versions:{"1.8.17":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.16":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.15":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.14":{uncompressed:"jquery-ui.js",
compressed:"jquery-ui.min.js"},"1.8.4":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.13":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.5":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.12":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.6":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.11":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.7":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},
"1.8.10":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.8":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.9":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.6.0":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.0":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.5.2":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.0":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.1":{uncompressed:"jquery-ui.js",
compressed:"jquery-ui.min.js"},"1.5.3":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.1":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.2":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.2":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.3":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"}},aliases:{1:"1.8.17","1.5":"1.5.3","1.6":"1.6.0","1.7":"1.7.3","1.8":"1.8.17","1.8.3":"1.8.4"}},mootools:{versions:{"1.3.0":{uncompressed:"mootools.js",
compressed:"mootools-yui-compressed.js"},"1.2.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.1.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.4.0":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.3.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.4.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},
"1.3.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.3":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.4.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.4":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.5":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.1.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"}},aliases:{1:"1.1.2","1.1":"1.1.2","1.2":"1.2.5",
"1.3":"1.3.2","1.4":"1.4.2","1.11":"1.1.1"}},yui:{versions:{"2.8.0r4":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.9.0":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.8.1":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.6.0":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.7.0":{uncompressed:"build/yuiloader/yuiloader.js",
compressed:"build/yuiloader/yuiloader-min.js"},"3.3.0":{uncompressed:"build/yui/yui.js",compressed:"build/yui/yui-min.js"},"2.8.2r1":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"}},aliases:{2:"2.9.0","2.6":"2.6.0","2.7":"2.7.0","2.8":"2.8.2r1","2.8.0":"2.8.0r4","2.8.2":"2.8.2r1","2.9":"2.9.0",3:"3.3.0","3.3":"3.3.0"}},prototype:{versions:{"1.6.1.0":{uncompressed:"prototype.js",compressed:"prototype.js"},"1.6.0.2":{uncompressed:"prototype.js",compressed:"prototype.js"},
"1.7.0.0":{uncompressed:"prototype.js",compressed:"prototype.js"},"1.6.0.3":{uncompressed:"prototype.js",compressed:"prototype.js"}},aliases:{1:"1.7.0.0","1.6":"1.6.1.0","1.6.0":"1.6.0.3","1.6.1":"1.6.1.0","1.7":"1.7.0.0","1.7.0":"1.7.0.0"}},jquery:{versions:{"1.2.3":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.2.6":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.3.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.3.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},
"1.3.2":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.2":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.3":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.4":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.5.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.5.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.5.2":{uncompressed:"jquery.js",
compressed:"jquery.min.js"},"1.6.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.2":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.3":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.4":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.7.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.7.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"}},aliases:{1:"1.7.1","1.2":"1.2.6","1.3":"1.3.2",
"1.4":"1.4.4","1.5":"1.5.2","1.6":"1.6.4","1.7":"1.7.1"}},dojo:{versions:{"1.3.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.4.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.3.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.5.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.4.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},
"1.3.2":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.2.3":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.6.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.5.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.7.0":{uncompressed:"dojo/dojo.js.uncompressed.js",compressed:"dojo/dojo.js"},"1.6.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},
"1.4.3":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.7.1":{uncompressed:"dojo/dojo.js.uncompressed.js",compressed:"dojo/dojo.js"},"1.7.2":{uncompressed:"dojo/dojo.js.uncompressed.js",compressed:"dojo/dojo.js"},"1.2.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.1.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"}},aliases:{1:"1.6.1","1.1":"1.1.1","1.2":"1.2.3","1.3":"1.3.2","1.4":"1.4.3","1.5":"1.5.1",
"1.6":"1.6.1","1.7":"1.7.2"}}};var Ga={af:!0,am:!0,az:!0,ar:!0,arb:"ar",bg:!0,bn:!0,ca:!0,cs:!0,cmn:"zh",da:!0,de:!0,el:!0,en:!0,en_gb:!0,es:!0,es_419:!0,et:!0,eu:!0,fa:!0,fi:!0,fil:!0,fr:!0,fr_ca:!0,gl:!0,ka:!0,gu:!0,he:"iw",hi:!0,hr:!0,hu:!0,hy:!0,id:!0,"in":"id",is:!0,it:!0,iw:!0,ja:!0,ji:"yi",jv:!1,jw:"jv",km:!0,kn:!0,ko:!0,lo:!0,lt:!0,lv:!0,ml:!0,mn:!0,mo:"ro",mr:!0,ms:!0,nb:"no",ne:!0,nl:!0,no:!0,pl:!0,pt:"pt_br",pt_br:!0,pt_pt:!0,ro:!0,ru:!0,si:!0,sk:!0,sl:!0,sr:!0,sv:!0,sw:!0,swh:"sw",ta:!0,te:!0,th:!0,tl:"fil",tr:!0,uk:!0,
ur:!0,vi:!0,yi:!1,zh:"zh_cn",zh_cn:!0,zh_hk:!0,zh_tw:!0,zsm:"ms",zu:!0};var Ha={1:"1.0","1.0":"current","1.1":"upcoming","1.2":"testing",41:"pre-45",42:"pre-45",43:"pre-45",44:"pre-45",46:"46.1","46.1":"46.2",48:"48.1",current:"51",upcoming:"52"};var Ia;function J(a,b){this.g=b===Ja?a:""}J.prototype.toString=function(){return this.g+""};J.prototype.K=!0;J.prototype.I=function(){return this.g.toString()};function Ka(a){return a instanceof J&&a.constructor===J?a.g:"type_error:TrustedResourceUrl"}
function La(a,b){var c=Ca(a);if(!Ma.test(c))throw Error("Invalid TrustedResourceUrl format: "+c);a=c.replace(Na,function(d,e){if(!Object.prototype.hasOwnProperty.call(b,e))throw Error('Found marker, "'+e+'", in format string, "'+c+'", but no valid label mapping found in args: '+JSON.stringify(b));d=b[e];return d instanceof H?Ca(d):encodeURIComponent(String(d))});return Oa(a)}
var Na=/%{(\w+)}/g,Ma=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i"),Pa=/^([^?#]*)(\?[^#]*)?(#[\s\S]*)?/;function Qa(a,b,c){a=La(a,b);a=Pa.exec(Ka(a).toString());b=a[3]||"";return Oa(a[1]+Ra("?",a[2]||"",c)+Ra("#",b))}var Ja={};
function Oa(a){if(void 0===Ia){var b=null;var c=A.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:za,createScript:za,createScriptURL:za})}catch(d){A.console&&A.console.error(d.message)}Ia=b}else Ia=b}a=(b=Ia)?b.createScriptURL(a):a;return new J(a,Ja)}
function Ra(a,b,c){if(null==c)return b;if("string"===typeof c)return c?a+encodeURIComponent(c):"";for(var d in c)if(Object.prototype.hasOwnProperty.call(c,d)){var e=c[d];e=Array.isArray(e)?e:[e];for(var h=0;h<e.length;h++){var f=e[h];null!=f&&(b||(b=a),b+=(b.length>a.length?"&":"")+encodeURIComponent(d)+"="+encodeURIComponent(String(f)))}}return b};var Sa=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function Ta(){};function Ua(a,b){for(var c in a)b.call(void 0,a[c],c,a)}var Va="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Wa(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var h=0;h<Va.length;h++)c=Va[h],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var Xa,Ya=B("CLOSURE_FLAGS"),Za=Ya&&Ya[610401301];Xa=null!=Za?Za:!1;function $a(){var a=A.navigator;return a&&(a=a.userAgent)?a:""}var K,ab=A.navigator;K=ab?ab.userAgentData||null:null;function bb(a,b){a:{var c=(a.ownerDocument&&a.ownerDocument.defaultView||A).document;if(c.querySelector&&(c=c.querySelector("script[nonce]"))&&(c=c.nonce||c.getAttribute("nonce"))&&cb.test(c))break a;c=""}c&&a.setAttribute("nonce",c);a.src=Ka(b)}var cb=/^[\w+/_-]+[=]{0,2}$/;var db=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function eb(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var h=a[c].substring(0,d);e=a[c].substring(d+1)}else h=a[c];b(h,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};function L(a){this.g=this.o=this.j="";this.u=null;this.m=this.h="";this.l=!1;var b;a instanceof L?(this.l=a.l,fb(this,a.j),this.o=a.o,this.g=a.g,gb(this,a.u),this.h=a.h,hb(this,ib(a.i)),this.m=a.m):a&&(b=String(a).match(db))?(this.l=!1,fb(this,b[1]||"",!0),this.o=M(b[2]||""),this.g=M(b[3]||"",!0),gb(this,b[4]),this.h=M(b[5]||"",!0),hb(this,b[6]||"",!0),this.m=M(b[7]||"")):(this.l=!1,this.i=new N(null,this.l))}
L.prototype.toString=function(){var a=[],b=this.j;b&&a.push(O(b,jb,!0),":");var c=this.g;if(c||"file"==b)a.push("//"),(b=this.o)&&a.push(O(b,jb,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.u,null!=c&&a.push(":",String(c));if(c=this.h)this.g&&"/"!=c.charAt(0)&&a.push("/"),a.push(O(c,"/"==c.charAt(0)?kb:lb,!0));(c=this.i.toString())&&a.push("?",c);(c=this.m)&&a.push("#",O(c,mb));return a.join("")};
L.prototype.resolve=function(a){var b=new L(this),c=!!a.j;c?fb(b,a.j):c=!!a.o;c?b.o=a.o:c=!!a.g;c?b.g=a.g:c=null!=a.u;var d=a.h;if(c)gb(b,a.u);else if(c=!!a.h){if("/"!=d.charAt(0))if(this.g&&!this.h)d="/"+d;else{var e=b.h.lastIndexOf("/");-1!=e&&(d=b.h.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var h=[],f=0;f<e.length;){var g=e[f++];"."==g?d&&f==e.length&&h.push(""):".."==g?((1<h.length||1==h.length&&""!=
h[0])&&h.pop(),d&&f==e.length&&h.push("")):(h.push(g),d=!0)}d=h.join("/")}else d=e}c?b.h=d:c=""!==a.i.toString();c?hb(b,ib(a.i)):c=!!a.m;c&&(b.m=a.m);return b};function fb(a,b,c){a.j=c?M(b,!0):b;a.j&&(a.j=a.j.replace(/:$/,""))}function gb(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.u=b}else a.u=null}function hb(a,b,c){b instanceof N?(a.i=b,nb(a.i,a.l)):(c||(b=O(b,ob)),a.i=new N(b,a.l))}
function M(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function O(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,pb),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function pb(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var jb=/[#\/\?@]/g,lb=/[#\?:]/g,kb=/[#\?]/g,ob=/[#\?@]/g,mb=/#/g;function N(a,b){this.h=this.g=null;this.i=a||null;this.j=!!b}
function P(a){a.g||(a.g=new Map,a.h=0,a.i&&eb(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}N.prototype.add=function(a,b){P(this);this.i=null;a=Q(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};function qb(a,b){P(a);b=Q(a,b);a.g.has(b)&&(a.i=null,a.h-=a.g.get(b).length,a.g.delete(b))}function rb(a,b){P(a);b=Q(a,b);return a.g.has(b)}
N.prototype.forEach=function(a,b){P(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};function sb(a,b){P(a);var c=[];if("string"===typeof b)rb(a,b)&&(c=c.concat(a.g.get(Q(a,b))));else for(a=Array.from(a.g.values()),b=0;b<a.length;b++)c=c.concat(a[b]);return c}N.prototype.set=function(a,b){P(this);this.i=null;a=Q(this,a);rb(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};
N.prototype.get=function(a,b){if(!a)return b;a=sb(this,a);return 0<a.length?String(a[0]):b};N.prototype.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=sb(this,d);for(var h=0;h<d.length;h++){var f=e;""!==d[h]&&(f+="="+encodeURIComponent(String(d[h])));a.push(f)}}return this.i=a.join("&")};function ib(a){var b=new N;b.i=a.i;a.g&&(b.g=new Map(a.g),b.h=a.h);return b}
function Q(a,b){b=String(b);a.j&&(b=b.toLowerCase());return b}function nb(a,b){b&&!a.j&&(P(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(qb(this,d),qb(this,e),0<c.length)){this.i=null;d=this.g;var h=d.set;e=Q(this,e);var f=c.length;if(0<f){for(var g=Array(f),k=0;k<f;k++)g[k]=c[k];f=g}else f=[];h.call(d,e,f);this.h+=c.length}},a));a.j=b};function tb(a,b){Ua(b,function(c,d){c&&"object"==typeof c&&c.K&&(c=c.I());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:ub.hasOwnProperty(d)?a.setAttribute(ub[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})}
var ub={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};function vb(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)}function wb(a){this.g=a||A.document||document};function xb(){};function yb(a,b){this.i=a;this.j=b;this.h=0;this.g=null}yb.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.i();return a};function zb(a,b){a.j(b);100>a.h&&(a.h++,b.next=a.g,a.g=b)};var Ab;
function Bb(){var a=A.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&-1==$a().indexOf("Presto")&&(a=function(){var e=vb(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var h=e.contentWindow;e=h.document;e.open();e.close();var f="callImmediate"+Math.random(),g="file:"==h.location.protocol?"*":h.location.protocol+"//"+h.location.host;e=C(function(k){if(("*"==g||k.origin==g)&&k.data==f)this.port1.onmessage()},this);
h.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){h.postMessage(f,g)}}});if("undefined"!==typeof a&&(Xa&&K&&0<K.brands.length||-1==$a().indexOf("Trident")&&-1==$a().indexOf("MSIE"))){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.H;c.H=null;e()}};return function(e){d.next={H:e};d=d.next;b.port2.postMessage(0)}}return function(e){A.setTimeout(e,0)}};function Cb(a){A.setTimeout(function(){throw a;},0)};function Db(){this.h=this.g=null}Db.prototype.add=function(a,b){var c=Eb.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};function Fb(){var a=Gb,b=null;a.g&&(b=a.g,a.g=a.g.next,a.g||(a.h=null),b.next=null);return b}var Eb=new yb(function(){return new Hb},function(a){return a.reset()});function Hb(){this.next=this.g=this.h=null}Hb.prototype.set=function(a,b){this.h=a;this.g=b;this.next=null};Hb.prototype.reset=function(){this.next=this.g=this.h=null};var Ib,Jb=!1,Gb=new Db;function Kb(a,b){Ib||Lb();Jb||(Ib(),Jb=!0);Gb.add(a,b)}function Lb(){if(A.Promise&&A.Promise.resolve){var a=A.Promise.resolve(void 0);Ib=function(){a.then(Mb)}}else Ib=function(){var b=Mb;"function"!==typeof A.setImmediate||A.Window&&A.Window.prototype&&(Xa&&K&&0<K.brands.length||-1==$a().indexOf("Edge"))&&A.Window.prototype.setImmediate==A.setImmediate?(Ab||(Ab=Bb()),Ab(b)):A.setImmediate(b)}}
function Mb(){for(var a;a=Fb();){try{a.h.call(a.g)}catch(b){Cb(b)}zb(Eb,a)}Jb=!1};function Nb(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function R(a){this.g=0;this.o=void 0;this.j=this.h=this.i=null;this.l=this.m=!1;if(a!=Ta)try{var b=this;a.call(void 0,function(c){S(b,2,c)},function(c){S(b,3,c)})}catch(c){S(this,3,c)}}function Ob(){this.next=this.i=this.h=this.j=this.g=null;this.l=!1}Ob.prototype.reset=function(){this.i=this.h=this.j=this.g=null;this.l=!1};var Pb=new yb(function(){return new Ob},function(a){a.reset()});function Qb(a,b,c){var d=Pb.get();d.j=a;d.h=b;d.i=c;return d}
R.prototype.then=function(a,b,c){return Rb(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};R.prototype.$goog_Thenable=!0;R.prototype.cancel=function(a){if(0==this.g){var b=new T(a);Kb(function(){Sb(this,b)},this)}};function Sb(a,b){if(0==a.g)if(a.i){var c=a.i;if(c.h){for(var d=0,e=null,h=null,f=c.h;f&&(f.l||(d++,f.g==a&&(e=f),!(e&&1<d)));f=f.next)e||(h=f);e&&(0==c.g&&1==d?Sb(c,b):(h?(d=h,d.next==c.j&&(c.j=d),d.next=d.next.next):Tb(c),Ub(c,e,3,b)))}a.i=null}else S(a,3,b)}
function Vb(a,b){a.h||2!=a.g&&3!=a.g||Wb(a);a.j?a.j.next=b:a.h=b;a.j=b}function Rb(a,b,c,d){var e=Qb(null,null,null);e.g=new R(function(h,f){e.j=b?function(g){try{var k=b.call(d,g);h(k)}catch(m){f(m)}}:h;e.h=c?function(g){try{var k=c.call(d,g);void 0===k&&g instanceof T?f(g):h(k)}catch(m){f(m)}}:f});e.g.i=a;Vb(a,e);return e.g}R.prototype.A=function(a){this.g=0;S(this,2,a)};R.prototype.B=function(a){this.g=0;S(this,3,a)};
function S(a,b,c){if(0==a.g){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.g=1;a:{var d=c,e=a.A,h=a.B;if(d instanceof R){Vb(d,Qb(e||Ta,h||null,a));var f=!0}else if(Nb(d))d.then(e,h,a),f=!0;else{if(wa(d))try{var g=d.then;if("function"===typeof g){Xb(d,g,e,h,a);f=!0;break a}}catch(k){h.call(a,k);f=!0;break a}f=!1}}f||(a.o=c,a.g=b,a.i=null,Wb(a),3!=b||c instanceof T||Yb(a,c))}}
function Xb(a,b,c,d,e){function h(k){g||(g=!0,d.call(e,k))}function f(k){g||(g=!0,c.call(e,k))}var g=!1;try{b.call(a,f,h)}catch(k){h(k)}}function Wb(a){a.m||(a.m=!0,Kb(a.u,a))}function Tb(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.j=null);return b}R.prototype.u=function(){for(var a;a=Tb(this);)Ub(this,a,this.g,this.o);this.m=!1};
function Ub(a,b,c,d){if(3==c&&b.h&&!b.l)for(;a&&a.l;a=a.i)a.l=!1;if(b.g)b.g.i=null,Zb(b,c,d);else try{b.l?b.j.call(b.i):Zb(b,c,d)}catch(e){$b.call(null,e)}zb(Pb,b)}function Zb(a,b,c){2==b?a.j.call(a.i,c):a.h&&a.h.call(a.i,c)}function Yb(a,b){a.l=!0;Kb(function(){a.l&&$b.call(null,b)})}var $b=Cb;function T(a){F.call(this,a)}E(T,F);T.prototype.name="cancel";/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
function U(a,b){this.l=[];this.D=a;this.C=b||null;this.j=this.i=!1;this.h=void 0;this.A=this.G=this.o=!1;this.m=0;this.g=null;this.u=0}E(U,xb);U.prototype.cancel=function(a){if(this.i)this.h instanceof U&&this.h.cancel();else{if(this.g){var b=this.g;delete this.g;a?b.cancel(a):(b.u--,0>=b.u&&b.cancel())}this.D?this.D.call(this.C,this):this.A=!0;this.i||(a=new V(this),ac(this),W(this,!1,a))}};U.prototype.B=function(a,b){this.o=!1;W(this,a,b)};function W(a,b,c){a.i=!0;a.h=c;a.j=!b;bc(a)}
function ac(a){if(a.i){if(!a.A)throw new cc(a);a.A=!1}}function dc(a,b,c,d){a.l.push([b,c,d]);a.i&&bc(a)}U.prototype.then=function(a,b,c){var d,e,h=new R(function(f,g){e=f;d=g});dc(this,e,function(f){f instanceof V?h.cancel():d(f);return ec},this);return h.then(a,b,c)};U.prototype.$goog_Thenable=!0;function fc(a){return Sa(a.l,function(b){return"function"===typeof b[1]})}var ec={};
function bc(a){if(a.m&&a.i&&fc(a)){var b=a.m,c=gc[b];c&&(A.clearTimeout(c.g),delete gc[b]);a.m=0}a.g&&(a.g.u--,delete a.g);b=a.h;for(var d=c=!1;a.l.length&&!a.o;){var e=a.l.shift(),h=e[0],f=e[1];e=e[2];if(h=a.j?f:h)try{var g=h.call(e||a.C,b);g===ec&&(g=void 0);void 0!==g&&(a.j=a.j&&(g==b||g instanceof Error),a.h=b=g);if(Nb(b)||"function"===typeof A.Promise&&b instanceof A.Promise)d=!0,a.o=!0}catch(k){b=k,a.j=!0,fc(a)||(c=!0)}}a.h=b;d&&(g=C(a.B,a,!0),d=C(a.B,a,!1),b instanceof U?(dc(b,g,d),b.G=!0):
b.then(g,d));c&&(b=new hc(b),gc[b.g]=b,a.m=b.g)}function cc(){F.call(this)}E(cc,F);cc.prototype.message="Deferred has already fired";cc.prototype.name="AlreadyCalledError";function V(){F.call(this)}E(V,F);V.prototype.message="Deferred was canceled";V.prototype.name="CanceledError";function hc(a){this.g=A.setTimeout(C(this.i,this),0);this.h=a}hc.prototype.i=function(){delete gc[this.g];throw this.h;};var gc={};function ic(a){var b;return(b=(a||document).getElementsByTagName("HEAD"))&&0!==b.length?b[0]:a.documentElement}function jc(){if(this&&this.L){var a=this.L;a&&"SCRIPT"==a.tagName&&kc(a,!0,this.M)}}function kc(a,b,c){null!=c&&A.clearTimeout(c);a.onload=function(){};a.onerror=function(){};a.onreadystatechange=function(){};b&&window.setTimeout(function(){a&&a.parentNode&&a.parentNode.removeChild(a)},0)}function lc(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);F.call(this,c);this.code=a}
E(lc,F);/*

 Copyright 2021 Google LLC
 This code is released under the MIT license.
 SPDX-License-Identifier: MIT
*/
function mc(a){return Qa(a.format,a.R,a.X||{})}
function nc(a){var b={timeout:3E4,attributes:{async:!1,defer:!1}},c=b.document||document,d=Ka(a).toString(),e=vb((new wb(c)).g,"SCRIPT"),h={L:e,M:void 0},f=new U(jc,h),g=null,k=null!=b.timeout?b.timeout:5E3;0<k&&(g=window.setTimeout(function(){kc(e,!0);var m=new lc(1,"Timeout reached for loading script "+d);ac(f);W(f,!1,m)},k),h.M=g);e.onload=e.onreadystatechange=function(){e.readyState&&"loaded"!=e.readyState&&"complete"!=e.readyState||(kc(e,b.V||!1,g),ac(f),W(f,!0,null))};e.onerror=function(){kc(e,
!0,g);var m=new lc(0,"Error while loading script "+d);ac(f);W(f,!1,m)};h=b.attributes||{};Wa(h,{type:"text/javascript",charset:"UTF-8"});tb(e,h);bb(e,a);ic(c).appendChild(e);return f}function oc(a,b,c){c=c||{};a=Qa(a,b,c);var d=nc(a);return new Promise(function(e){dc(d,e,null)})};/*

 Copyright 2021 Google LLC
 This code is released under the MIT license.
 SPDX-License-Identifier: MIT

*/
function pc(){return new Promise(function(a){"undefined"===typeof window||"complete"===document.readyState?a():window.addEventListener?(document.addEventListener("DOMContentLoaded",a,!0),window.addEventListener("load",a,!0)):window.attachEvent?window.attachEvent("onload",a):"function"!==typeof window.onload?window.onload=a:window.onload=function(b){if(window.onload)window.onload(b);a()}})};var X="",Y="",qc,Z,rc=null,sc;function tc(a){var b=a,c,d=a.match(/^testing-/);d&&(b=b.replace(/^testing-/,""));a=b;do{if(b===Ha[b])throw Error("Infinite loop in version mapping: "+b);(c=Ha[b])&&(b=c)}while(c);c=(d?"testing-":"")+b;a="pre-45"==b?a:c;return{version:a,S:c}}
function uc(a){var b=Da[sc].loader,c=tc(a);return oc(b,{version:c.S}).then(function(){var d=B("google.charts.loader.versionSpecific.load")||B("google.charts.loader.VersionSpecific.load")||B("google.charts.loader.publicLoad")||B("google.charts.versionSpecific.load");if(!d)throw Error("Bad version: "+a);rc=function(e){e=d(c.version,e);if(null==e||null==e.then){var h=B("google.charts.loader.publicSetOnLoadCallback")||B("google.charts.versionSpecific.setOnLoadCallback");e=new Promise(function(f){h(f)});
e.then=h}return e}})}function vc(a){"string"===typeof a&&(a=[a]);Array.isArray(a)&&0!==a.length||(a=Ea);var b=[];a.forEach(function(c){c=c.toLowerCase();b=b.concat(c.split(/[\s,]+\s*/))});return b}function wc(a){a=a||"";for(var b=a.replace(/-/g,"_").toLowerCase();"string"===typeof b;)a=b,b=Ga[b],b===a&&(b=!1);b||(a.match(/_[^_]+$/)?(a=a.replace(/_[^_]+$/,""),a=wc(a)):a="en");return a}
function xc(a){a=a||"";""!==X&&X!==a&&(console.warn(" Attempting to load version '"+a+"' of Google Charts, but the previously loaded '"+(X+"' will be used instead.")),a=X);return X=a||""}function yc(a){a=a||"";""!==Y&&Y!==a&&(console.warn(" Attempting to load Google Charts for language '"+a+"', but the previously loaded '"+(Y+"' will be used instead.")),a=Y);"en"===a&&(a="");return Y=a||""}function zc(a){var b={},c;for(c in a)b[c]=a[c];return b}
function Ac(a,b){b=zc(b);b.domain=sc;b.callback=Bc(b.callback);a=xc(a);var c=b.language;c=yc(wc(c));b.language=c;if(!qc){if(b.enableUrlSettings&&window.URLSearchParams)try{a=(new URLSearchParams(top.location.search)).get("charts-version")||a}catch(d){console.info("Failed to get charts-version from top URL",d)}qc=uc(a)}b.packages=vc(b.packages);return Z=qc.then(function(){return rc(b)})}
function Cc(a){if(!Z)throw Error("Must call google.charts.load before google.charts.setOnLoadCallback");return a?Z.then(a):Z}D("google.charts.safeLoad",function(a){return Dc(Object.assign({},a,{safeMode:!0}))});function Dc(){var a=la.apply(0,arguments),b=0;"visualization"===a[b]&&b++;var c="current";if("string"===typeof a[b]||"number"===typeof a[b])c=String(a[b]),b++;var d={};wa(a[b])&&(d=a[b]);return Ac(c,d)}D("google.charts.load",Dc);D("google.charts.setOnLoadCallback",Cc);
var Ec=I("https://maps.googleapis.com/maps/api/js?jsapiRedirect=true"),Fc=I("https://maps-api-ssl.google.com/maps?jsapiRedirect=true&file=googleapi");
function Gc(a,b,c){console.warn("Loading Maps API with the jsapi loader is deprecated.");c=c||{};a=c.key||c.client;var d=c.libraries,e=function(g){for(var k={},m=0;m<g.length;m++){var n=g[m];k[n[0]]=n[1]}return k}(c.other_params?c.other_params.split("&").map(function(g){return g.split("=")}):[]),h=Object.assign({},{key:a,W:d},e),f="2"===b?Fc:Ec;Z=new Promise(function(g){var k=Bc(c&&c.callback);oc(f,{},h).then(k).then(g)})}var Hc=I("https://www.gstatic.com/inputtools/js/ita/inputtools_3.js");
function Ic(a,b,c){wa(c)&&c.packages?(Array.isArray(c.packages)?c.packages:[c.packages]).includes("inputtools")?(console.warn('Loading "elements" with the jsapi loader is deprecated.\nPlease load '+(Hc+" directly.")),Z=new Promise(function(d){var e=Bc(c&&c.callback);oc(Hc,{},{}).then(e).then(d)})):console.error('Loading "elements" other than "inputtools" is unsupported.'):console.error("google.load of elements was invoked without specifying packages")}var Jc=I("https://ajax.googleapis.com/ajax/libs/%{module}/%{version}/%{file}");
function Kc(a,b){var c;do{if(a===b[a])throw Error("Infinite loop in version mapping for version "+a);(c=b[a])&&(a=c)}while(c);return a}
function Lc(a,b,c){var d=Fa[a];if(d){b=Kc(b,d.aliases);d=d.versions[b];if(!d)throw Error("Unknown version, "+b+", of "+a+".");var e={module:a,version:b||"",file:d.compressed};b=Ka(mc({format:Jc,R:e})).toString();console.warn("Loading modules with the jsapi loader is deprecated.\nPlease load "+(a+" directly from "+b+"."));Z=new Promise(function(h){var f=Bc(c&&c.callback);oc(Jc,e).then(f).then(h)})}else setTimeout(function(){throw Error('Module "'+a+'" is not supported.');},0)}
function Bc(a){return function(){if("function"===typeof a)a();else if("string"===typeof a&&""!==a)try{var b=B(a);if("function"!==typeof b)throw Error("Type of '"+a+"' is "+typeof b+".");b()}catch(c){throw Error("Callback of "+a+" failed with: "+c);}}}function Mc(){var a=la.apply(0,arguments);switch(a[0]){case "maps":Gc.apply(null,ea(a));break;case "elements":Ic.apply(null,ea(a));break;case "visualization":Dc.apply(null,ea(a));break;default:Lc.apply(null,ea(a))}}D("google.loader.LoadFailure",!1);
if(sc)console.warn("Google Charts loader.js should only be loaded once.");else{Y=X="";rc=Z=qc=null;B("google.load")||(D("google.load",Mc),D("google.setOnLoadCallback",Cc));var Nc=document.getElementsByTagName("script"),Oc=(document.currentScript||Nc[Nc.length-1]).getAttribute("src"),Pc=new L(Oc),Qc=Pc.g;sc=Qc=Qc.match(/^www\.gstatic\.cn/)?"gstatic.cn":"gstatic.com";var Rc=new N(Pc.i.toString()),Sc=Rc.get("callback");if("string"===typeof Sc){var Tc=Bc(Sc);pc().then(Tc)}var Uc=Rc.get("autoload");if("string"===
typeof Uc)try{if(""!==Uc)for(var Vc=JSON.parse(Uc).modules,Wc=0;Wc<Vc.length;Wc++){var Xc=Vc[Wc];Mc(Xc.name,Xc.version,Xc)}}catch(a){throw Error("Autoload failed with: "+a);}};}).call(this);
