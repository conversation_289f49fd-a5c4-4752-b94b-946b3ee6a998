const branchService = require('../services/branch-service')
class BranchController {
  async createBranch(req, res) {
    try {
      const branch = await branchService.createBranch(req.body);
      res.status(201).send(branch);
    } catch (error) {
      res.status(400).send({ error: error.message });
    }
  }

  async getAllBranches(req, res) {
    try {
      const branches = await branchService.getAllBranches();
      res.status(200).send(branches);
    } catch (error) {
      res.status(500).send({ error: error.message });
    }
  }

  async getBranchById(req, res) {
    try {
      const branch = await branchService.getBranchById(req.params.id);
      if (branch) {
        res.status(200).send(branch);
      } else {
        res.status(404).send({ message: 'Branch not found' });
      }
    } catch (error) {
      res.status(500).send({ error: error.message });
    }
  }

  async updateBranch(req, res) {
    try {
      const branch = await branchService.updateBranch(req.params.id, req.body);
      if (branch) {
        res.status(200).send(branch);
      } else {
        res.status(404).send({ message: 'Branch not found' });
      }
    } catch (error) {
      res.status(400).send({ error: error.message });
    }
  }

  async deleteBranch(req, res) {
    try {
      const branch = await branchService.deleteBranch(req.params.id);
      if (branch) {
        res.status(200).send({ message: 'Branch deleted successfully' });
      } else {
        res.status(404).send({ message: 'Branch not found' });
      }
    } catch (error) {
      res.status(500).send({ error: error.message });
    }
  }
}

module.exports = new BranchController();
