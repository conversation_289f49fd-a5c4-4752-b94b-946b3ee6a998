const AddContainerDespatchService = require('../services/containerD-service');

const createContainerDespatch = async (req, res) => {
  try {
    const containerDespatchData = req.body;
    const containerDespatch = await AddContainerDespatchService.createContainerDespatch(containerDespatchData);
    res.status(201).json(containerDespatch);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

const getContainerDespatchById = async (req, res) => {
  try {
    const containerDespatchId = req.params.id;
    const containerDespatch = await AddContainerDespatchService.getContainerDespatchById(containerDespatchId);
    res.status(200).json(containerDespatch);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

const getAllContainerDespatches = async (req, res) => {
  try {
    const containerDespatches = await AddContainerDespatchService.getAllContainerDespatches();
    res.status(200).json(containerDespatches);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

const updateContainerDespatch = async (req, res) => {
  try {
    const containerDespatchId = req.params.id;
    const containerDespatchData = req.body;
    const updatedContainerDespatch = await AddContainerDespatchService.updateContainerDespatch(containerDespatchId, containerDespatchData);
    res.status(200).json(updatedContainerDespatch);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

const deleteContainerDespatch = async (req, res) => {
  try {
    const containerDespatchId = req.params.id;
    await AddContainerDespatchService.deleteContainerDespatch(containerDespatchId);
    res.status(204).send();
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

module.exports = {
  createContainerDespatch,
  getContainerDespatchById,
  getAllContainerDespatches,
  updateContainerDespatch,
  deleteContainerDespatch,
};
