router.post('/api/invoices', async (req, res) => {
  try {
    const invoice = new Invoice(req.body.document);
    await invoice.save();
    res.status(201).json({ success: true, invoice });
  } catch (error) {
    console.error('Invoice save error:', error); // This will print the real error in your terminal
    res.status(500).json({ success: false, message: error.message, error }); // Send error details to frontend
  }
});