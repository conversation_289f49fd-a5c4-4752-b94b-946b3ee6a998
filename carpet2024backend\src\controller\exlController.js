var productModal = require('../model/phase-1/packageListExcel');
const slugify = require("slugify");
const readXlsxFile = require("read-excel-file/node");
const excel = require("exceljs");
const path = require("path");
const { count } = require('console');
const fs = require('fs');

const uploadExl = async (req, res) => {
  try {
    const { websiteId, impoterNo, impoterName } = req.body; // Fetch impoterNo from request body


    if (req.file === undefined) {
      return res.status(400).send("Please upload an excel file!");
    }

    const filePath = path.resolve(__dirname, `../../public/all-pdf/${req.file.filename}`);

    readXlsxFile(filePath).then(async (rows) => {
      // Skip header row
      rows.shift();

      const products = rows.map((row) => ({
        Date: row[0],
        BaleNo: row[1],
        PcsNo: row[2],
        Quality: row[3],
        Design: row[4],
        Colour: row[5],
        Width: row[6],
        Length: row[7],
        Area: row[8],
        InvoiceNo: row[9],
        ImpoterCode: row[10],
        Remark: row[11],
        containerNo: impoterNo,
        impoterName: impoterName

      }));
      let isExist = await productModal.findOne({ InvoiceNo: products[0].InvoiceNo, impoterName: products[0].impoterName });

      try {
        if (!isExist) {
          const insertedProducts = await productModal.insertMany(products);
          // console.log('Bulk data uploaded successfully:', insertedProducts);
          res.status(200).send({
            message: 'Bulk data uploaded successfully',
            success: true,
            data: insertedProducts
          });
        } else {

          fs.unlink(filePath, (err) => {
            if (err) {
              console.error('Error deleting file:', err);
              return;
            }
            console.log('File deleted successfully');
          });
          res.status(200).send('Invoice has been allerady exist');
        }
      } catch (error) {
        // console.error('Error uploading bulk data:', error);
        res.status(500).send('Error uploading bulk data');
      }
    });
  } catch (error) {
    // console.error('Error:', error);
    res.status(500).send({
      message: "Could not upload the file: " + req.file.originalname,
    });
  }
};

const getAllData = async (req, res) => {
  try {
    let { InvoiceNo, impoterName } = req.body;

    // const exlCountData = 
    console.log("----line ")
    let allProducts;
    if (InvoiceNo == null && impoterName == null) {
      allProducts = await productModal.find();
    } else {
       allProducts = await productModal.find({ InvoiceNo: InvoiceNo, impoterName: impoterName });
    }
    console.log("line 59", allProducts)
    res.status(200).json(allProducts);
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};
const exldata = async (req, res) => {
  const qualityCount = {};

  let { InvoiceNo, impoterName } = req.body;

  const exlCountData = await productModal.find({ InvoiceNo: InvoiceNo, impoterName: impoterName });

  exlCountData.forEach(dt => {
    if (qualityCount[dt.Quality]) {
      qualityCount[dt.Quality].total++;
      qualityCount[dt.Quality].area += parseFloat(dt.Area);
    } else {
      qualityCount[dt.Quality] = {
        total: 1,
        area: parseFloat(dt.Area),
        design: dt.Design // Assuming there's a 'Design' property in your data
      };
    }
  });

  for (const key in qualityCount) {
    qualityCount[key].multiValue = qualityCount[key].total * qualityCount[key].area;
  }

  const result = Object.entries(qualityCount).map(([quality, Count]) => ({
    name: quality.toLowerCase(),
    total: Count.total,
    area: (Count.area)?.toFixed(2),
    multiValue: (Count.total * Count.area).toFixed(2),
    design: Count.design // Added 'design' property
  }));

  const totalData = {
    total: Object.values(qualityCount).reduce((acc, curr) => acc + curr.total, 0),
    areaTotal: Object.values(qualityCount).reduce((acc, curr) => acc + curr.area, 0).toFixed(2),
    multiValueTotal: Object.values(qualityCount).reduce((acc, curr) => acc + curr.multiValue, 0).toFixed(2),
  };

  // Assuming 'totalData' also needs to include design totals
  totalData.designTotal = Object.values(qualityCount).reduce((acc, curr) => {
    acc[curr.design] = (acc[curr.design] || 0) + curr.total;
    return acc;
  }, {});

  res.json({ result, totalData });
};

module.exports = {
  uploadExl,
  getAllData,
  exldata,

};



