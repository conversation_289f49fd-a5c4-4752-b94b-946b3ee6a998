const mongoose = require('mongoose');

async function dropIndexes() {
    try {
        await mongoose.connect('mongodb://localhost:27017/test');
        console.log('Connected to MongoDB');
        
        // Drop all indexes except _id
        await mongoose.connection.collection('carpetreceiveds').dropIndexes();
        console.log('Successfully dropped indexes from carpetreceiveds collection');

        // Create only the indexes we want
        await mongoose.connection.collection('carpetreceiveds').createIndex(
            { receiveNo: 1 },
            { unique: true }
        );
        console.log('Created new index on receiveNo');

        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

dropIndexes();
