const mongoose = require('mongoose');

async function dropIndex() {
    try {
        await mongoose.connect('mongodb://localhost:27017/test');
        
        // Drop the existing unique index on carpetreceiveds collection
        await mongoose.connection.db.collection('carpetreceiveds').dropIndex('issueNo_1');
        console.log('Successfully dropped unique index on carpetreceiveds collection');
        
        process.exit(0);
    } catch (error) {
        console.error('Error dropping index:', error);
        process.exit(1);
    }
}

dropIndex();
