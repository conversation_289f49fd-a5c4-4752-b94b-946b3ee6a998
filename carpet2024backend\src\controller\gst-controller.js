const gstService = require('../services/gst-service');

async function createGST(req, res) {
  try {
    const { toDate, fromDate, gstRate } = req.body;
    const newGST = await gstService.createGST(toDate, fromDate, gstRate);
    res.status(201).json(newGST);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}

async function getGSTById(req, res) {
  try {
    const { id } = req.params;
    const gst = await gstService.getGSTById(id);
    if (!gst) {
      return res.status(404).json({ message: 'GST not found' });
    }
    res.status(200).json(gst);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}

async function getGST(req, res) {
  try {
    const gst = await gstService.getGST();
    if (!gst) {
      return res.status(404).json({ message: 'GST not found' });
    }
    res.status(200).json(gst);
  } catch (error) {
    res.status(500).json({ error: error.message }); 
  }
}

async function updateGST(req, res) {
  try {
    const { id } = req.params;
    const updates = req.body;
    const updatedGST = await gstService.updateGST(id, updates);
    res.status(200).json(updatedGST);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}

async function deleteGST(req, res) {
  try {
    const { id } = req.params;
    const message = await gstService.deleteGST(id);
    res.status(200).json({ message });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}

module.exports = {
  createGST,
  getGSTById,
  updateGST,
  deleteGST,
  getGST
};
