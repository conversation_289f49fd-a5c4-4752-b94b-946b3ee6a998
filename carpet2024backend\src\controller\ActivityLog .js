const { ActivityLog } = require('../model/phase-1/activitylog'); // Adjust the path as necessary

const getAllActivityLogs = async (req, res) => {
  try {
    const logs = await ActivityLog.find().sort({ timestamp: -1 }); // Sort logs by most recent
    res.status(200).json(logs);
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    res.status(500).json({ message: 'Error fetching activity logs', error });
  }
};

module.exports = {
  getAllActivityLogs,
};
