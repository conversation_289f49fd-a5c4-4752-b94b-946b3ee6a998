const mongoose = require('mongoose');
const exportPackingService = require('../../services/manifacturing/exportPacking-service');

// Get export packing lists by invoice number
const getExportPackingsByInvoiceNo = async (req, res) => {
  try {
    const { invoiceNo } = req.params;

    // Validate invoiceNo
    if (!mongoose.Types.ObjectId.isValid(invoiceNo)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoiceNo',
        error: 'invoiceNo must be a valid ObjectId'
      });
    }

    const packingLists = await exportPackingService.getByInvoiceNo(invoiceNo);

    res.status(200).json({
      success: true,
      message: 'Export packing lists retrieved successfully',
      data: packingLists
    });
  } catch (error) {
    console.error('Error getting export packing lists by invoice number:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get export packing lists by invoice number',
      error: error.message
    });
  }
};

// Create a new export packing list
const createExportPacking = async (req, res) => {
  try {
    console.log('Received export packing list data:', JSON.stringify(req.body, null, 2));

    const { invoiceNo, baleNo, date, items, totalArea, totalTArea, totalPcses } = req.body;

    // Validate required fields
    const requiredFields = ['invoiceNo', 'baleNo', 'date', 'items', 'totalArea', 'totalTArea', 'totalPcses'];
    for (const field of requiredFields) {
      if (!req.body[field]) {
        return res.status(400).json({
          success: false,
          message: `Missing required field: ${field}`,
          error: `${field} is required`
        });
      }
    }

    // Validate invoiceNo
    if (!mongoose.Types.ObjectId.isValid(invoiceNo)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoiceNo',
        error: 'invoiceNo must be a valid ObjectId'
      });
    }

    // Validate items
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid items',
        error: 'Items must be a non-empty array'
      });
    }

    const newExportPacking = await exportPackingService.createExportPacking({
      invoiceNo,
      baleNo,
      date,
      items,
      totalArea,
      totalTArea,
      totalPcses
    });

    res.status(201).json({
      success: true,
      message: 'Export packing list created successfully',
      data: newExportPacking
    });
  } catch (error) {
    console.error('Error creating export packing list:', error);

    let statusCode = 500;
    if (error.message.includes('required') || error.message.includes('must be')) {
      statusCode = 400;
    } else if (error.name === 'ValidationError') {
      statusCode = 400;
    } else if (error.name === 'MongoServerError' && error.code === 11000) {
      statusCode = 409;
    }

    res.status(statusCode).json({
      success: false,
      message: 'Failed to create export packing list',
      error: error.message
    });
  }
};

// Get all export packing lists
const getAllExportPackings = async (req, res) => {
  try {
    const allExportPackings = await exportPackingService.getAllExportPackings();
    res.status(200).json({
      success: true,
      data: allExportPackings
    });
  } catch (error) {
    console.error('Error getting export packing lists:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get export packing lists',
      error: error.message
    });
  }
};

// Get export packing list by ID
const getExportPackingById = async (req, res) => {
  try {
    const exportPacking = await exportPackingService.getExportPackingById(req.params.id);
    res.status(200).json({
      success: true,
      data: exportPacking
    });
  } catch (error) {
    console.error('Error getting export packing list:', error);
    if (error.message === 'Export packing list not found') {
      return res.status(404).json({
        success: false,
        message: 'Export packing list not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to get export packing list',
      error: error.message
    });
  }
};

// Update export packing list by ID
const updateExportPacking = async (req, res) => {
  try {
    const { invoiceNo, baleNo, date, items, totalArea, totalTArea, totalPcses } = req.body;

    // Validate invoiceNo if provided
    if (invoiceNo && !mongoose.Types.ObjectId.isValid(invoiceNo)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid invoiceNo',
        error: 'invoiceNo must be a valid ObjectId'
      });
    }

    const updatedData = {
      invoiceNo,
      baleNo,
      date,
      items,
      totalArea,
      totalTArea,
      totalPcses
    };

    const updatedExportPacking = await exportPackingService.updateExportPacking(req.params.id, updatedData);

    res.status(200).json({
      success: true,
      message: 'Export packing list updated successfully',
      data: updatedExportPacking
    });
  } catch (error) {
    console.error('Error updating export packing list:', error);
    if (error.message === 'Export packing list not found') {
      return res.status(404).json({
        success: false,
        message: 'Export packing list not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to update export packing list',
      error: error.message
    });
  }
};

// Delete export packing list by ID
const deleteExportPacking = async (req, res) => {
  try {
    await exportPackingService.deleteExportPacking(req.params.id);
    res.status(200).json({
      success: true,
      message: 'Export packing list deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting export packing list:', error);
    if (error.message === 'Export packing list not found') {
      return res.status(404).json({
        success: false,
        message: 'Export packing list not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to delete export packing list',
      error: error.message
    });
  }
};

module.exports = {
  createExportPacking,
  getAllExportPackings,
  getExportPackingById,
  updateExportPacking,
  deleteExportPacking,
  getExportPackingsByInvoiceNo
};