const roleRepository = require('../repositories/role-repository');

class RoleController {
  async createRole(req, res) {
    try {
      const {name} = req.body; 
      const role = await roleRepository.createRole({name});
      res.status(201).json(role);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getRoleById(req, res) {
    try {
      const roleId = req.params.id;
      const role = await roleRepository.getRoleById(roleId);
      res.status(200).json(role);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getRoleByName(req, res) {
    try {
      const roleName = req.body.name;
      const role = await roleRepository.getRoleByName(roleName.toUpperCase());
      res.status(200).json(role);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllRoles(req, res) {
    try {
      const roles = await roleRepository.getAllRoles();
      res.status(200).json(roles);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateRole(req, res) {
    try {
      const roleId = req.params.id;
      const {name} = req.body; 
      const updatedRole = await roleRepository.updateRole(roleId,{ name: name.toUpperCase()});
      res.status(200).json(updatedRole);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteRole(req, res) {
    try {
      const roleId = req.params.id;
      const deletedRole = await roleRepository.deleteRole(roleId);
      res.status(200).json(deletedRole);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new RoleController();
