const MaterialLagatService = require('../services/materialLagat-service');

const createMaterialLagat = async (req, res) => {
  try {
    const materialLagatData = req.body;
    const newMaterialLagat = await MaterialLagatService.createMaterialLagat(materialLagatData);
    res.status(201).json(newMaterialLagat);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllMaterialLagats = async (req, res) => {
  try {
    const materialLagats = await MaterialLagatService.getAllMaterialLagats();
    res.status(200).json(materialLagats);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getMaterialLagatById = async (req, res) => {
  try {
    const { id } = req.params;
    const materialLagat = await MaterialLagatService.getMaterialLagatById(id);
    if (!materialLagat) {
      return res.status(404).json({ message: "Material Lagat not found" });
    }
    res.status(200).json(materialLagat);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateMaterialLagat = async (req, res) => {
  try {
    const { id } = req.params;
    const materialLagatData = req.body;
    const updatedMaterialLagat = await MaterialLagatService.updateMaterialLagat(id, materialLagatData);
    res.status(200).json(updatedMaterialLagat);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteMaterialLagat = async (req, res) => {
  try {
    const { id } = req.params;
    await MaterialLagatService.deleteMaterialLagat(id);
    res.status(200).json({ message: "Material Lagat deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateField = async (req, res) => {
  try {
    const { id } = req.params;
    const { field, value } = req.body;
    const updatedMaterialLagat = await MaterialLagatService.updateField(id, field, value);
    res.status(200).json(updatedMaterialLagat);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteField = async (req, res) => {
  try {
    const { id } = req.params;
    const { field } = req.body;
    await MaterialLagatService.deleteField(id, field);
    res.status(200).json({ message: "Field deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createMaterialLagat,
  getAllMaterialLagats,
  getMaterialLagatById,
  updateMaterialLagat,
  deleteMaterialLagat,
  updateField,
  deleteField,
};
