// controllers/invoiceController.js
const invoiceService = require('../../services/manifacturing/invoice-service');

class InvoiceController {
    // Create a new invoice
    async createInvoice(req, res) {
        try {
            const invoiceData = req.body;
            const createdInvoice = await invoiceService.createInvoice(invoiceData);
            res.status(201).json(createdInvoice);
        } catch (error) {
            res.status(500).json({ message: 'Error creating invoice', error });
        }
    }

    // Get all invoices with sorting
    async getAllInvoices(req, res) {
        try {
            const { sortBy, order } = req.query; // Retrieve sortBy and order from query params
            const invoices = await invoiceService.getAllInvoices(sortBy, order);
            res.status(200).json(invoices);
        } catch (error) {
            res.status(500).json({ message: 'Error fetching invoices', error });
        }
    }

    // Get invoice by ID
    async getInvoiceById(req, res) {
        try {
            const invoiceId = req.params.id;
            const invoice = await invoiceService.getInvoiceById(invoiceId);
            if (!invoice) {
                res.status(404).json({ message: 'Invoice not found' });
            } else {
                res.status(200).json(invoice);
            }
        } catch (error) {
            res.status(500).json({ message: 'Error fetching invoice', error });
        }
    }

    // Update invoice
    async updateInvoice(req, res) {
        try {
            const invoiceId = req.params.id;
            const invoiceData = req.body;
            const updatedInvoice = await invoiceService.updateInvoice(invoiceId, invoiceData);
            if (!updatedInvoice) {
                res.status(404).json({ message: 'Invoice not found' });
            } else {
                res.status(200).json(updatedInvoice);
            }
        } catch (error) {
            res.status(500).json({ message: 'Error updating invoice', error });
        }
    }

    // Delete invoice
    async deleteInvoice(req, res) {
        try {
            const invoiceId = req.params.id;
            const deletedInvoice = await invoiceService.deleteInvoice(invoiceId);
            if (!deletedInvoice) {
                res.status(404).json({ message: 'Invoice not found' });
            } else {
                res.status(200).json({ message: 'Invoice deleted successfully' });
            }
        } catch (error) {
            res.status(500).json({ message: 'Error deleting invoice', error });
        }
    }
}

module.exports = new InvoiceController();

