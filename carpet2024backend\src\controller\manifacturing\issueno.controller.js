const issueNoService = require('../../services/manifacturing/issueNo-service');

class IssueNoController {
  async create(req, res) {
    try {
      const issueNo = await issueNoService.createIssueNo(req.body);
      res.status(201).json(issueNo);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getAll(req, res) {
    try {
      const issueNos = await issueNoService.getAllIssueNos();
      res.status(200).json(issueNos);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getById(req, res) {
    try {
      const issueNo = await issueNoService.getIssueNoById(req.params.id);
      if (!issueNo) {
        return res.status(404).json({ message: 'IssueNo not found' });
      }
      res.status(200).json(issueNo);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async update(req, res) {
    try {
      const issueNo = await issueNoService.updateIssueNo(req.params.id, req.body);
      if (!issueNo) {
        return res.status(404).json({ message: 'IssueNo not found' });
      }
      res.status(200).json(issueNo);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async delete(req, res) {
    try {
      const issueNo = await issueNoService.deleteIssueNo(req.params.id);
      if (!issueNo) {
        return res.status(404).json({ message: 'IssueNo not found' });
      }
      res.status(204).json();
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = new IssueNoController();
