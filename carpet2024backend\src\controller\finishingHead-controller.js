const finishingHeadService = require('../services/finishingHead-service');

const createFinishingHead = async (req, res) => {
  try {
    const finishingHeadData = req.body;
    const newFinishingHead = await finishingHeadService.createFinishingHead(finishingHeadData);
    res.status(201).json(newFinishingHead);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllFinishingHeads = async (req, res) => {
  try {
    const finishingHeads = await finishingHeadService.getAllFinishingHeads();
    res.status(200).json(finishingHeads);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getFinishingHeadById = async (req, res) => {
  try {
    const finishingHead = await finishingHeadService.getFinishingHeadById(req.params.id);
    if (!finishingHead) {
      return res.status(404).json({ message: "Finishing Head not found" });
    }
    res.status(200).json(finishingHead);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateFinishingHead = async (req, res) => {
  try {
    const { id } = req.params;
    const finishingHeadData = req.body;
    const updatedFinishingHead = await finishingHeadService.updateFinishingHead(id, finishingHeadData);
    res.status(200).json(updatedFinishingHead);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteFinishingHead = async (req, res) => {
  try {
    const { id } = req.params;
    await finishingHeadService.deleteFinishingHead(id);
    res.status(200).json({ message: "Finishing Head deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createFinishingHead,
  getAllFinishingHeads,
  getFinishingHeadById,
  updateFinishingHead,
  deleteFinishingHead
};
