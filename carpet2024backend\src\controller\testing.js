 const ContainerRecieved = require('../model/phase-1/container-rvc');
 
 const getHistry = async (req,resp)=>{
    try {
        const startDate = new Date('2014-03-11T00:00:00.000Z'); // Start date
const endDate = new Date('2015-03-11T00:00:00.000Z'); // End date
const gerCarpetNo = "5001146"; // Specific GerCarpetNo to find

// Use a variable to decide which filter to apply
const useCarpetNo = true; // Set this to true to use GerCarpetNo, false to use date range

// Build the match condition dynamically
const matchCondition = useCarpetNo 
    ? { "containerItem.GerCarpetNo": gerCarpetNo }
    : { "containerItem.Date": { $gte: startDate, $lt: endDate } };

let data = await ContainerRecieved.aggregate([
    {
        $unwind: "$containerItem" // Unwind the containerItem array
    },
    {
        $match: matchCondition
    },
    {
        $lookup: {
            from: "createchallans",
            let: { gerCarpetNo: "$containerItem.GerCarpetNo" },
            pipeline: [
                { $unwind: "$carpetList" },
                {
                    $match: {
                        $expr: {
                            $eq: [{ $toString: "$carpetList.barcodeNo" }, "$$gerCarpetNo"]
                        }
                    }
                },
                {
                    $lookup: {
                        from: "billforwholesellers",
                        let: { challanNo: "$challanNo" },
                        pipeline: [
                            { $unwind: "$challanNo" },
                            {
                                $match: {
                                    $expr: {
                                        $eq: ["$challanNo.challanNumber", "$$challanNo"]
                                    }
                                }
                            },
                            {
                                $project: {
                                    _id: 0,
                                    billNo: 1,
                                    chooseAdate: 1,
                                    wholesellerName: 1,
                                    isBillDeleted: 1,
                                    "challanNo.challanNumber": 1,
                                    "challanNo.status": 1
                                }
                            }
                        ],
                        as: "billDetails"
                    }
                },
                {
                    $project: {
                        _id: 0,
                        challanNo: 1,
                        chooseAdate: 1,
                        wholeseller: 1,
                        retailerOutlet: 1,
                        "carpetList.barcodeNo": 1,
                        "carpetList.amount": 1,
                        "carpetList.size": 1,
                        "carpetList.area": 1,
                        "carpetList.evkPrice": 1,
                        "carpetList.status": 1,
                        billDetails: 1
                    }
                }
            ],
            as: "carpetHistory"
        }
    },
    {
        $unwind: {
            path: "$carpetHistory",
            preserveNullAndEmptyArrays: true // Preserve documents without matching carpetHistory
        }
    },
    {
        $unwind: {
            path: "$carpetHistory.billDetails",
            preserveNullAndEmptyArrays: true // Preserve documents without matching billDetails
        }
    },
    {
        $project: {
            _id: 0, // Exclude the _id field if not needed
            impoterName: 1,
            expensesAmount: 1,
            totalArea: 1,
            espPrice: 1,
            blPdf: 1,
            "GerCarpetNo": "$containerItem.GerCarpetNo",
            "QualityDesign": "$containerItem.QualityDesign",
            "Color": "$containerItem.Color",
            "CCode": "$containerItem.CCode",
            "QCode": "$containerItem.QCode",
            "Size": "$containerItem.Size",
            "SCore": "$containerItem.SCore",
            "Area": "$containerItem.Area",
            "EvKPrice": "$containerItem.EvKPrice",
            "Amount": "$containerItem.Amount",
            "InvoiceNo": "$containerItem.InvoiceNo",
            "status": "$containerItem.status",
            "Date": "$containerItem.Date",
            "challanNo": "$carpetHistory.challanNo",
            "chooseAdate": "$carpetHistory.chooseAdate",
            "wholeseller": "$carpetHistory.wholeseller",
            "retailerOutlet": "$carpetHistory.retailerOutlet",
            "barcodeNo": "$carpetHistory.carpetList.barcodeNo",
            "carpetAmount": "$carpetHistory.carpetList.amount",
            "carpetSize": "$carpetHistory.carpetList.size",
            "carpetArea": "$carpetHistory.carpetList.area",
            "carpetEvKPrice": "$carpetHistory.carpetList.evkPrice",
            "carpetStatus": "$carpetHistory.carpetList.status",
            "billNo": "$carpetHistory.billDetails.billNo",
            "billChooseAdate": "$carpetHistory.billDetails.chooseAdate",
            "billWholesellerName": "$carpetHistory.billDetails.wholesellerName",
            "billIsBillDeleted": "$carpetHistory.billDetails.isBillDeleted",
            "billChallanNumber": "$carpetHistory.billDetails.challanNo.challanNumber",
            "billChallanStatus": "$carpetHistory.billDetails.challanNo.status"
        }
    }
])

        
        
        


// let data =await ContainerRecieved.aggregate([
//     {
//         $unwind: "$containerItem" // Unwind the containerItem array
//     },
//     {
//         $match: {
//             "containerItem.Date": {
//                 $gte: startDate,
//                 $lt: endDate
//             }
//         }
//     },
//     {
//         $lookup: {
//             from: "createchallans", // Collection name of CreateChallan
//             let: { gerCarpetNo: "$containerItem.GerCarpetNo" },
//             pipeline: [
//                 { $unwind: "$carpetList" },
//                 {
//                     $match: {
//                         $expr: {
//                             $eq: [{ $toString: "$carpetList.barcodeNo" }, "$$gerCarpetNo"]
//                         }
//                     }
//                 },
//                 {
//                     $project: {
//                         _id: 0,
//                         challanNo: 1,
//                         chooseAdate: 1,
//                         wholeseller: 1,
//                         retailerOutlet: 1,
//                         "carpetList.barcodeNo": 1,
//                         "carpetList.amount": 1,
//                         "carpetList.size": 1,
//                         "carpetList.area": 1,
//                         "carpetList.evkPrice": 1,
//                         "carpetList.status": 1
//                     }
//                 }
//             ],
//             as: "carpetHistory"
//         }
//     },
//     {
//         $unwind: {
//             path: "$carpetHistory",
//             preserveNullAndEmptyArrays: true // Preserve documents without matching carpetHistory
//         }
//     },
//     {
//         $project: {
//             _id: 0, // Exclude the _id field if not needed
//             impoterName: 1,
//             expensesAmount: 1,
//             totalArea: 1,
//             espPrice: 1,
//             blPdf: 1,
//             "GerCarpetNo": "$containerItem.GerCarpetNo",
//             "QualityDesign": "$containerItem.QualityDesign",
//             "Color": "$containerItem.Color",
//             "CCode": "$containerItem.CCode",
//             "QCode": "$containerItem.QCode",
//             "Size": "$containerItem.Size",
//             "SCore": "$containerItem.SCore",
//             "Area": "$containerItem.Area",
//             "EvKPrice": "$containerItem.EvKPrice",
//             "Amount": "$containerItem.Amount",
//             "InvoiceNo": "$containerItem.InvoiceNo",
//             "status": "$containerItem.status",
//             "Date": "$containerItem.Date",
//             "challanNo": "$carpetHistory.challanNo",
//             "chooseAdate": "$carpetHistory.chooseAdate",
//             "wholeseller": "$carpetHistory.wholeseller",
//             "retailerOutlet": "$carpetHistory.retailerOutlet",
//             "barcodeNo": "$carpetHistory.carpetList.barcodeNo",
//             "carpetAmount": "$carpetHistory.carpetList.amount",
//             "carpetSize": "$carpetHistory.carpetList.size",
//             "carpetArea": "$carpetHistory.carpetList.area",
//             "carpetEvKPrice": "$carpetHistory.carpetList.evkPrice",
//             "carpetStatus": "$carpetHistory.carpetList.status"
//         }
//     }
// ]);

return resp.send({
    data:data
})

    } catch (error) {
      return resp.send({
        error:error
    })  
    }
 }

 module.exports={getHistry}