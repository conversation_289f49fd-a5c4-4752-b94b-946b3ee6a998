const rawMaterialGroupService = require('../services/rawMaterial-service');
const path = require('path');

const createRawMaterialGroup = async (req, res) => {
  try {
    const { Group, Item, Description, Count, woolQuality, Color, Details } = req.body;

    let fileName = '';
    if (req.files && req.files.length > 0) {
      fileName = req.files[0].filename; // Use only the file name
    }

    const newRawMaterialGroup = await rawMaterialGroupService.createRawMaterialGroup({
      Group,
      Item,
      Description,
      Count,
      woolQuality,
      Color,
      Details,
      uploadedFile: fileName,
    });

    res.status(201).json(newRawMaterialGroup);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllRawMaterialGroups = async (req, res) => {
  try {
    const rawMaterialGroups = await rawMaterialGroupService.getAllRawMaterialGroups();
    res.status(200).json(rawMaterialGroups);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getRawMaterialGroupById = async (req, res) => {
  try {
    const rawMaterialGroup = await rawMaterialGroupService.getRawMaterialGroupById(req.params.id);
    if (!rawMaterialGroup) {
      return res.status(404).json({ message: "Raw material group not found" });
    }
    res.status(200).json(rawMaterialGroup);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateRawMaterialGroup = async (req, res) => {
  try {
    const { Group, Item, Description, Count, woolQuality, Color, Details } = req.body;

    let fileName = '';
    if (req.files && req.files.length > 0) {
      fileName = req.files[0].filename; // Use only the file name
    }

    const updatedRawMaterialGroup = await rawMaterialGroupService.updateRawMaterialGroup(req.params.id, {
      Group,
      Item,
      Description,
      Count,
      woolQuality,
      Color,
      Details,
      uploadedFile: fileName, // Store only the file name
    });

    if (!updatedRawMaterialGroup) {
      return res.status(404).json({ message: "Raw material group not found" });
    }

    res.status(200).json(updatedRawMaterialGroup);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteRawMaterialGroup = async (req, res) => {
  try {
    const deletedRawMaterialGroup = await rawMaterialGroupService.deleteRawMaterialGroup(req.params.id);
    if (!deletedRawMaterialGroup) {
      return res.status(404).json({ message: "Raw material group not found" });
    }
    res.status(200).json({ message: "Raw material group deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createRawMaterialGroup,
  getAllRawMaterialGroups,
  getRawMaterialGroupById,
  updateRawMaterialGroup,
  deleteRawMaterialGroup
};
