const carpetService = require('../services/carpet-service');

class CarpetController {
  async createCarpet(req, res) {
    try {
      const carpetData = req.body;
      const carpet = await carpetService.createCarpet(carpetData);
      res.status(201).json(carpet);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getCarpetById(req, res) {
    try {
      const carpetId = req.params.id;
      const carpet = await carpetService.getCarpetById(carpetId);
      res.status(200).json(carpet);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllCarpets(req, res) {
    try {
      const carpets = await carpetService.getAllCarpets();
      res.status(200).json(carpets);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateCarpet(req, res) {
    try {
      const carpetId = req.params.id;
      const carpetData = req.body;
      const updatedCarpet = await carpetService.updateCarpet(carpetId, carpetData);
      res.status(200).json(updatedCarpet);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteCarpet(req, res) {
    try {
      const carpetId = req.params.id;
      const deletedCarpet = await carpetService.deleteCarpet(carpetId);
      res.status(200).json(deletedCarpet);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new CarpetController();
