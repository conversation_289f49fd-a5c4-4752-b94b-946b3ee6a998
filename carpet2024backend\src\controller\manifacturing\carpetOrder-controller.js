const carpetOrderissueService = require('../../services/manifacturing/carpetOrder-service');
const path = require('path');
const uploadFiles = require('../../middleware/allfile-middleware');

const createCarpetOrderissue = async (req, res) => {
  uploadFiles(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: "Error uploading file", error: err.message });
    }
    try {
      const { Br_issueNo, date, branch, buyerOrder, weaver, quality, design, borderColour, size, khapSize, pcs, area, rate, itemId, AreaIn} = req.body;
      // Make MapOrderNo truly optional with a default empty string
      const MapOrderNo = req.body.MapOrderNo || '';
      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename;
      }
      const numericArea = parseFloat(area); // Safely extract number
      const numericRate = parseFloat(rate);
      // Round amount to nearest integer and always show .00 as a string
      const amount = isNaN(numericArea) || isNaN(numericRate) ? '0.00' : (Math.round(numericArea * numericRate)).toFixed(2);

// Map frontend AreaIn to model-compatible values
const areaInMapped = AreaIn === 'Sq.Feet' ? 'Sq.Feet' : AreaIn === 'Sq.Yard' ? 'Sq.Yard' : 'Sq.Feet' ; // Default to Sq.Feet if invalid

      const newCarpetOrderissue = await carpetOrderissueService.createCarpetOrderissue({
        Br_issueNo,
        date,
        branch,
        buyerOrder,
        weaver,
        quality,
        design,
        borderColour,
        size,
        khapSize,
        pcs,
        area,
        rate,
        amount, // ✅ Add this line
        MapOrderNo,
        itemId,
        areaIn: areaInMapped, // Include mapped areaIn
        uploadFile: fileName,
      });
if(newCarpetOrderissue){

  res.status(201).json(newCarpetOrderissue);
}

    } catch (error) {
      res.status(500).json({ message: "Error creating order", error });
    }
  });
};

const getAllCarpetOrderissues = async (_, res) => {
  try {
    const carpetOrderissues = await carpetOrderissueService.getAllCarpetOrderissues();
    res.status(200).json(carpetOrderissues);
  } catch (error) {
    res.status(500).json({ message: "Error fetching orders", error });
  }
};

const getCarpetOrderissueById = async (req, res) => {
  try {
    const carpetOrderissue = await carpetOrderissueService.getCarpetOrderissueById(req.params.id);
    if (!carpetOrderissue) {
      return res.status(404).json({ message: "Order not found" });
    }
    res.status(200).json(carpetOrderissue);
  } catch (error) {
    res.status(500).json({ message: "Error fetching order", error });
  }
};

const updateCarpetOrderissue = async (req, res) => {
  uploadFiles(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: "Error uploading file", error: err.message });
    }
    try {
      console.log('Update request body:', req.body);
      console.log('Update request files:', req.files);
      console.log('Update request params:', req.params);

      let existingData;
      try {
        existingData = req.body.existingData ? JSON.parse(req.body.existingData) : [];
      } catch (parseError) {
        console.error('Error parsing existingData:', parseError);
        existingData = [];
      }


      const { Br_issueNo,itemId, date, buyerOrder, weaver, quality, design, borderColour, size, khapSize, pcs, area, rate, AreaIn } = req.body;

      // Get current order to check PCSReceived
      const currentOrder = await carpetOrderissueService.getCarpetOrderissueById(req.params.id);
      const pcsReceived = currentOrder?.PCSReceived || 0;
      const newPcs = parseInt(pcs) || 0;

      console.log('🔍 Backend PCS Validation:', {
        currentOrderId: req.params.id,
        pcsReceived,
        newPcs,
        currentOrderPcs: currentOrder?.pcs
      });

      // Validate that new pcs is not less than already received
      // Allow pcs to be equal to or greater than pcsReceived
      if (pcsReceived > 0 && newPcs < pcsReceived) {
        console.log('❌ PCS validation failed: newPcs < pcsReceived');
        return res.status(400).json({
          message: `PCS cannot be less than already received (${pcsReceived}). Current value: ${newPcs}`,
          error: 'PCS_VALIDATION_ERROR'
        });
      }

      // Additional validation: Check if newPcs exceeds available pieces from buyer order
      if (currentOrder && currentOrder.buyerOrder && currentOrder.buyerOrder.items) {
        const buyerOrderItem = currentOrder.buyerOrder.items.find(item =>
          item._id.toString() === (itemId || currentOrder.itemId).toString()
        );

        if (buyerOrderItem) {
          const currentIssuedPcs = parseInt(currentOrder.pcs) || 0;
          const availablePending = parseInt(buyerOrderItem.PcsesPending) || 0;
          const maxAllowedPcs = currentIssuedPcs + availablePending;

          console.log('🔍 Buyer Order Validation:', {
            currentIssuedPcs,
            availablePending,
            maxAllowedPcs,
            newPcs
          });

          // Only validate if maxAllowedPcs > 0 to avoid false positives
          if (maxAllowedPcs > 0 && newPcs > maxAllowedPcs) {
            console.log('❌ PCS validation failed: newPcs > maxAllowedPcs');
            return res.status(400).json({
              message: `PCS cannot exceed ${maxAllowedPcs} (Current Issued: ${currentIssuedPcs} + Available Pending: ${availablePending})`,
              error: 'PCS_VALIDATION_ERROR'
            });
          }
        }
      }
      // Make MapOrderNo truly optional with a default empty string
      const MapOrderNo = req.body.MapOrderNo || '';

      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename;
      }
      const numericArea = parseFloat(area);
      const numericRate = parseFloat(rate);
      // Round amount to nearest integer and always show .00 as a string
      const amount = isNaN(numericArea) || isNaN(numericRate) ? '0.00' : (Math.round(numericArea * numericRate)).toFixed(2);
// Map frontend AreaIn to model-compatible values
const areaInMapped = AreaIn === 'Sq.Feet' ? 'Sq.Feet' : AreaIn === 'Sq.Yard' ? 'Sq.Yard' : 'Sq.Feet' ; // Default to Sq.Feet if invalid

      const updatedOrderData = {
        Br_issueNo,
        date,
        itemId,
        buyerOrder,
        weaver,
        quality,
        design,
        borderColour,
        size,
        khapSize,
        pcs,
        area,
        rate,
        amount,
        MapOrderNo,
        areaIn: areaInMapped,
       
        existingData
      };


      const updatedCarpetOrderissue = await carpetOrderissueService.updateCarpetOrderissue(req.params.id, updatedOrderData);

      if (!updatedCarpetOrderissue) {
        return res.status(404).json({ message: "Order not found" });
      }

      res.status(200).json(updatedCarpetOrderissue);
    } catch (error) {
      res.status(500).json({ message: "Error updating order", error });
    }
  });
};

const updateCarpetOrderissueField = async (req, res) => {
  try {
    const { field, value } = req.body;
    const updatedOrder = await carpetOrderissueService.updateCarpetOrderissueField(req.params.id, field, value);
    if (!updatedOrder) {
      return res.status(404).json({ message: "Order not found" });
    }
    res.status(200).json(updatedOrder);
  } catch (error) {
    res.status(500).json({ message: "Error updating order field", error });
  }
};

const deleteCarpetOrderissueField = async (req, res) => {
  try {
    const  field  = req.body;
    const updatedOrder = await carpetOrderissueService.deleteCarpetOrderissueField(req.params.id, field);
    if (!updatedOrder) {
      return res.status(404).json({ message: "Order not found" });
    }
    res.status(200).json(updatedOrder);
  } catch (error) {
    res.status(500).json({ message: "Error deleting order field", error });
  }
};

const deleteCarpetOrderissue = async (req, res) => {
  try {
    const deletedCarpetOrderissue = await carpetOrderissueService.deleteCarpetOrderissue(req.params.id);
    if (!deletedCarpetOrderissue) {
      return res.status(404).json({ message: "Order not found" });
    }
    res.status(200).json({ message: "Order deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting order", error });
  }
};

const getPopulate =async (_, res)=>{
  try {
    const carpetOrderissues = await carpetOrderissueService.getPopulate();
    res.status(200).json(carpetOrderissues);
  } catch (error) {
    res.status(500).json({ message: "Error fetching orders", error });
  }
}

const updatePCSFields = async (req, res) => {
  try {
    const { issueId } = req.params;
    const updatedIssue = await carpetOrderissueService.updatePCSFields(issueId);
    if (!updatedIssue) {
      return res.status(404).json({ message: "Issue not found" });
    }
    res.status(200).json(updatedIssue);
  } catch (error) {
    res.status(500).json({ message: "Error updating PCS fields", error });
  }
};

const initializePCSFields = async (req, res) => {
  try {
    const result = await carpetOrderissueService.initializePCSFields();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: "Error initializing PCS fields", error });
  }
};

const fixPCSFields = async (req, res) => {
  try {
    const CarpetOrderissue = require('../../model/phase-4/carpetOrderIssue');

    // Get all issues and fix their PCS fields
    const issues = await CarpetOrderissue.find({});
    let fixedCount = 0;

    for (const issue of issues) {
      const totalPcs = parseInt(issue.pcs) || 0;

      // Since carpetreceiveds collection is empty, all pieces should be waiting
      await CarpetOrderissue.findByIdAndUpdate(
        issue._id,
        {
          $set: {
            PCSReceived: 0,
            PCSWaitingToBeReceived: totalPcs
          }
        }
      );
      fixedCount++;
    }

    res.status(200).json({
      message: "PCS fields fixed successfully",
      fixedCount: fixedCount,
      details: "Set PCSReceived=0 and PCSWaitingToBeReceived=pcs for all issues"
    });
  } catch (error) {
    res.status(500).json({ message: "Error fixing PCS fields", error: error.message });
  }
};

const fixBuyerOrderNaNValues = async (req, res) => {
  try {
    const result = await carpetOrderissueService.fixBuyerOrderNaNValues();
    res.status(200).json({
      message: "Buyer order NaN values fixed successfully",
      ...result
    });
  } catch (error) {
    console.error('Error fixing buyer order NaN values:', error);
    res.status(500).json({
      message: "Error fixing buyer order NaN values",
      error: error.message
    });
  }
};

module.exports = {
  createCarpetOrderissue,
  getAllCarpetOrderissues,
  getCarpetOrderissueById,
  updateCarpetOrderissue,
  updateCarpetOrderissueField,
  deleteCarpetOrderissueField,
  deleteCarpetOrderissue,
  getPopulate,
  updatePCSFields,
  initializePCSFields,
  fixPCSFields,
  fixBuyerOrderNaNValues
};
