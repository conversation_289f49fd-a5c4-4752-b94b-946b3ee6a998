// controllers/materialDeyingController.js
const materialDeyingService = require('../../services/manifacturing/materialDeying-service');

class MaterialDeyingController {
  async create(req, res) {
    try {
      const materialDeying = await materialDeyingService.create(req.body);
      res.status(201).json(materialDeying);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async findById(req, res) {
    try {
      const materialDeying = await materialDeyingService.findById(req.params.id);
      if (materialDeying) {
        res.status(200).json(materialDeying);
      } else {
        res.status(404).json({ message: 'Material Deying not found' });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async findAll(req, res) {
    try {
      const materials = await materialDeyingService.findAll();
      res.status(200).json(materials);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async update(req, res) {
    try {
      const materialDeying = await materialDeyingService.update(req.params.id, req.body);
      if (materialDeying) {
        res.status(200).json(materialDeying);
      } else {
        res.status(404).json({ message: 'Material Deying not found' });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async delete(req, res) {
    try {
      const materialDeying = await materialDeyingService.delete(req.params.id);
      if (materialDeying) {
        res.status(200).json({ message: 'Material Deying deleted successfully' });
      } else {
        res.status(404).json({ message: 'Material Deying not found' });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new MaterialDeyingController();
