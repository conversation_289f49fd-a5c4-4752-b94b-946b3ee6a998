const mapRateService = require('../services/mapRate-service');

const createMapRate = async (req, res) => {
  try {
    const mapRateData = req.body;
    const newMapRate = await mapRateService.createMapRate(mapRateData);
    res.status(201).json(newMapRate);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllMapRates = async (req, res) => {
  try {
    const mapRates = await mapRateService.getAllMapRates();
    res.status(200).json(mapRates);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getMapRateById = async (req, res) => {
  try {
    const mapRate = await mapRateService.getMapRateById(req.params.id);
    if (!mapRate) {
      return res.status(404).json({ message: "Map Rate not found" });
    }
    res.status(200).json(mapRate);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateMapRate = async (req, res) => {
  try {
    const { id } = req.params;
    const mapRateData = req.body;
    const updatedMapRate = await mapRateService.updateMapRate(id, mapRateData);
    res.status(200).json(updatedMapRate);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteMapRate = async (req, res) => {
  try {
    const { id } = req.params;
    await mapRateService.deleteMapRate(id);
    res.status(200).json({ message: "Map Rate deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createMapRate,
  getAllMapRates,
  getMapRateById,
  updateMapRate,
  deleteMapRate
};
