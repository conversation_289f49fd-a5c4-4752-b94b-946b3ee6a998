const ContainerReceived = require("../model/phase-1/container-rvc");

// const getsAllStock = async (req, res) => {
//   try {
// let getAllSoled = await ContainerReceived.aggregate([
//   {
//     $unwind: "$containerItem"
//   },
//   {
//     $lookup: {
//       from: "createchallans",
//       let: { gerCarpetNo: { $toString: "$containerItem.GerCarpetNo" } },
//       pipeline: [
//         { $unwind: "$carpetList" },
//         {
//           $match: {
//             $expr: { $eq: [{ $toString: "$carpetList.barcodeNo" }, "$$gerCarpetNo"] }
//           }
//         },
//         {
//           $lookup: {
//             from: "billforwholesellers",
//             localField: "challanNo",
//             foreignField: "challanNo.challanNumber",
//             as: "billData"
//           }
//         },
//         {
//           $unwind: {
//             path: "$billData",
//             preserveNullAndEmptyArrays: false
//           }
//         },
//         {
//           $project: {
//             barcodeNo: "$carpetList.barcodeNo",
//             amount: "$carpetList.amount",
//             size: "$carpetList.size",
//             area: "$carpetList.area",
//             evkPrice: "$carpetList.evkPrice",
//             status: "$carpetList.status",
//             wholeseller: 1,
//             retailerOutlet: 1,
//             chooseAdate: 1,
//             challanNo: 1,
//             billNo: "$billData.billNo",
//             billCustomer: "$billData.wholesellerName",
//             isBillDeleted: "$billData.isBillDeleted",
//             billChooseAdate: "$billData.chooseAdate",
//             billAmount: "$billData.amount"
//           }
//         }
//       ],
//       as: "matchedData"
//     }
//   },
//   {
//     $unwind: {
//       path: "$matchedData",
//       preserveNullAndEmptyArrays: false
//     }
//   },
//   {
//     $project: {
//       _id: 0,
//       GerCarpetNo: "$containerItem.GerCarpetNo",
//       QualityDesign: "$containerItem.QualityDesign",
//       Color: "$containerItem.Color",
//       CCode: "$containerItem.CCode",
//       QCode: "$containerItem.QCode",
//       Size: "$containerItem.Size",
//       SCore: "$containerItem.SCore",
//       Area: "$containerItem.Area",
//       EvKPrice: "$containerItem.EvKPrice",
//       Amount: "$containerItem.Amount",
//       InvoiceNo: "$containerItem.InvoiceNo",
//       ContainerStatus: "$containerItem.status",
//       ContainerDate: "$containerItem.Date",
//       MatchedBarcodeNo: "$matchedData.barcodeNo",
//       MatchedAmount: "$matchedData.amount",
//       MatchedSize: "$matchedData.size",
//       MatchedArea: "$matchedData.area",
//       MatchedEvKPrice: "$matchedData.evkPrice",
//       MatchedStatus: "$matchedData.status",
//       Wholeseller: "$matchedData.wholeseller",
//       RetailerOutlet: "$matchedData.retailerOutlet",
//       ChooseAdate: "$matchedData.chooseAdate",
//       ChallanNo: "$matchedData.challanNo",
//       BillNo: "$matchedData.billNo",
//       BillChooseAdate: "$matchedData.billChooseAdate",
//       BillAmount: "$matchedData.billAmount",
//       BillCustomer: "$matchedData.billCustomer",
//       IsBillDeleted: "$matchedData.isBillDeleted"
//     }
//   }
// ]);

//     if (getAllSoled) {
//       return res.status(200).json({
//         message: "Carpet stock details",
//         success: true,
//         data: getAllSoled,
//       });
//     } else {
//       return res.status(404).json({
//         message: "Not found",
//         success: false,
//       });
//     }
//   } catch (error) {
//     console.error(error);  // Log the error for debugging
//     return res.status(500).json({
//       message: error.message,
//       success: false,  // Indicate that the operation was not successful
//     });
//   }
// };

// module.exports = { getsAllStock };
const BillForWholesellers = require("../model/phase-2/billWholeseller");

const getsAllStock = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let getAllSoled = await BillForWholesellers.aggregate([
      {
        $unwind: "$challanNo", // Unwind the array of challanNo
      },
      {
        $lookup: {
          from: "createchallans",
          localField: "challanNo.challanNumber", // Match the challan number in the bill
          foreignField: "challanNo", // Match it with the challanNo in createchallans
          as: "challanData",
        },
      },
      {
        $unwind: "$challanData", // Unwind the matched data from createchallans
      },
      {
        $unwind: "$challanData.carpetList", // Unwind the carpetList within createchallans
      },
      {
        $lookup: {
          from: "containerreceiveds",
          let: {
            barcodeNo: { $toString: "$challanData.carpetList.barcodeNo" },
          }, // Convert barcodeNo to string
          pipeline: [
            { $unwind: "$containerItem" },
            {
              $match: {
                $expr: { $eq: ["$containerItem.GerCarpetNo", "$$barcodeNo"] }, // Match with GerCarpetNo
              },
            },
          ],
          as: "carpetData",
        },
      },
      {
        $unwind: "$carpetData", // Unwind the matched data from containerreceiveds
      },
      {
        $project: {
          _id: 0,
          BillNo: "$billNo",
          BillChooseAdate: "$chooseAdate",
          BillCustomer: "$wholesellerName",
          IsBillDeleted: "$isBillDeleted",
          ChallanNo: "$challanNo.challanNumber",
          ChooseAdate: "$challanData.chooseAdate",
          RetailerOutlet: "$challanData.retailerOutlet",
          Wholeseller: "$challanData.wholeseller",
          GerCarpetNo: "$carpetData.containerItem.GerCarpetNo",
          QualityDesign: "$carpetData.containerItem.QualityDesign",
          Color: "$carpetData.containerItem.Color",
          CCode: "$carpetData.containerItem.CCode",
          QCode: "$carpetData.containerItem.QCode",
          Size: "$carpetData.containerItem.Size",
          SCore: "$carpetData.containerItem.SCore",
          Area: "$carpetData.containerItem.Area",
          EvKPrice: "$carpetData.containerItem.EvKPrice",
          Amount: "$carpetData.containerItem.Amount",
          InvoiceNo: "$carpetData.containerItem.InvoiceNo",
          ContainerStatus: "$carpetData.containerItem.status",
          ContainerDate: "$carpetData.containerItem.Date",
          MatchedBarcodeNo: "$challanData.carpetList.barcodeNo",
          MatchedAmount: "$challanData.carpetList.amount",
          MatchedSize: "$challanData.carpetList.size",
          MatchedArea: "$challanData.carpetList.area",
          MatchedEvKPrice: "$challanData.carpetList.evkPrice",
          MatchedStatus: "$challanData.carpetList.status",
        },
      },
      { $skip: skip },
      { $limit: limit },
    ]);

    // Get total count for pagination
    const totalResults = await BillForWholesellers.countDocuments();

    if (getAllSoled.length > 0) {
      console.log( {
        totalResults,
        currentPage: page,
        totalPages: Math.ceil(totalResults / limit),
        pageSize: limit,
      })
      return res.status(200).json({
        message: "Carpet stock details",
        success: true,
        data: getAllSoled,
        pagination: {
          totalResults,
          currentPage: page,
          totalPages: Math.ceil(totalResults / limit),
          pageSize: limit,
        },
      });

    } else {
      return res.status(404).json({
        message: "Not found",
        success: false,
      });
    }
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: error.message,
      success: false,
    });
  }
};

module.exports = { getsAllStock };
