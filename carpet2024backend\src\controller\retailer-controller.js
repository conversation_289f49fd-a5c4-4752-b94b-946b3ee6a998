// controllers/retailerController.js

const retailerService = require("../services/retailer-service");

async function createRetailer(req, res) {
  try {
    const retailer = await retailerService.createRetailer(req.body);
    res.status(201).json(retailer);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function getRetailers(req, res) {
  try {
    const retailers = await retailerService.getRetailers();
    res.json(retailers);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

async function getRetailerById(req, res) {
  try {
    const retailer = await retailerService.getRetailerById(req.params.id);
    if (retailer) {
      res.json(retailer);
    } else {
      res.status(404).json({ message: "Retailer not found" });
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

async function updateRetailer(req, res) {
  try {
    const retailer = await retailerService.updateRetailer(
      req.params.id,
      req.body
    );
    res.json(retailer);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function deleteRetailer(req, res) {
  try {
    await retailerService.deleteRetailer(req.params.id);
    res.json({ message: "Retailer deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

async function addRetailerPrice(req, res) {
  try {
    let id = req.params.id;
    let bodyData = req.body.addOnPrice;
    let updatedData = await retailerService.addRetailerPrice(id, bodyData);
    if (updatedData.acknowledged == true && updatedData.modifiedCount == 1) {
      return res.status(200).send({
        message: "Price add success",
        success: true,
      });
    }

    return res.status(200).send({
      message: "Failed!, Something went wrong",
      success: false,
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

module.exports = {
  createRetailer,
  getRetailers,
  getRetailerById,
  updateRetailer,
  deleteRetailer,
  addRetailerPrice
};
