const PdfService = require('../services/pdfDetailes-service');

class PdfController {
  async createPdf(req, res) {
    try {
      const { fieldName, originalname, encoding, mimetype, destination, filename, path, size } = req.file;

      const pdf = await PdfService.createPdf({ fieldName, originalname, encoding, mimetype, destination, filename, path, size });
      res.status(201).json(pdf);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async getAllPdf(req, res) {
    
    try {
      
      const allPdf = await PdfService.getAllPdf();
      res.status(200).json(allPdf);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getPdfById(req, res) {
    const { id } = req.params;
    try {
      const pdf = await PdfService.getPdfById(id);
      if (!pdf) {
        res.status(404).json({ message: 'PDF not found' });
      } else {
        res.json(pdf);
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async updatePdf(req, res) {
    const { id } = req.params;
    const newData = req.body;
    try {
      const updatedPdf = await PdfService.updatePdf(id, newData);
      if (!updatedPdf) {
        res.status(404).json({ message: 'PDF not found' });
      } else {
        res.json(updatedPdf);
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async deletePdf(req, res) {
    const { id } = req.params;
    try {
      const deletedPdf = await PdfService.deletePdf(id);
      if (!deletedPdf) {
        res.status(404).json({ message: 'PDF not found' });
      } else {
        res.json({ message: 'PDF deleted successfully' });
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
}

module.exports = new PdfController();
