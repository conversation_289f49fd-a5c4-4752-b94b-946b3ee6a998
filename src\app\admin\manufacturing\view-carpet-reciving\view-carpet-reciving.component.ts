import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ManufactureService } from '../../../services/manufacture.service';
import { SweetalertService } from '../../../services/sweetalert.service';
import { MasterService } from '../../../services/master.service';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { EditCarpetModalComponent } from './modals/edit-carpet-modal/edit-carpet-modal.component';
import { PaymentModalComponent } from './modals/payment-modal/payment-modal.component';

export interface CarpetData {
  _id: string;
  SrNo: number;
  CarpetNo: string;
  CDate: string;
  Weaver: string;
  IssueNo: string;
  IDate: string;
  Quality: string;
  Design: string;
  BorderColour: string;
  Size: string;
  Pcs: number;
  AreaIn: string;
  Area: string;
  Rate: number;
   Amount: string;
  Deduction: string;
  TDS_percent: number;
  Commission_percent: number;
  TDS: string;
  Commission: string;
  NetAmount: string;
  BankName: string;
  ChequeNoOrRGSno: string;
  PayedAmount: string;
  PaymentDt: string;
  Remarks: string;
  branchName: string;
  branchCode: string;
}

@Component({
  selector: 'app-view-carpet-reciving',
  templateUrl: './view-carpet-reciving.component.html',
  styleUrls: ['./view-carpet-reciving.component.css'],
})
export class ViewCarpetRecivingComponent implements OnInit, AfterViewInit {
  dataSource = new MatTableDataSource<CarpetData>([]);  displayedColumns: string[] = [
    'SrNo',
    'CarpetNo',
    'CDate',
    'IssueNo',
    'IDate',
    'Weaver',
    'Quality',
    'Design',
    'BorderColour',
    'Size',
    'Pcs',
    'Area',
    'Rate',
    'Deduction',
    'Amount',
    'TDS',
    'Commission',
    'NetAmount',
    'PaymentDt',
    'BankName',
    'ChequeNoOrRGSno',
    'PayedAmount',
    'Remarks',
    'Actions', 
  ];
  isLoading = true; // Add loading state
  errorMessage: string | null = null; // Add error message for UI feedback

  // Filter properties
  originalData: CarpetData[] = []; // Store original unfiltered data
  uniqueWeavers: string[] = []; // List of unique weavers for dropdown
  uniqueYears: string[] = []; // List of unique years for dropdown
  selectedWeaver: string = ''; // Currently selected weaver filter
  selectedMonth: string = ''; // Currently selected month filter
  selectedYear: string = ''; // Currently selected year filter
  generalSearchTerm: string = ''; // General search term

  constructor(
    private manufactureService: ManufactureService,
    private _fb: FormBuilder,
    private adminServices: MasterService,
    private alert: SweetalertService,
    private customeService: CustomeServiceService,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    public dialog: MatDialog
  ) {}
  filterForm!: FormGroup;

  ngOnInit(): void {
    // Populate availableYears with last 10 years
    const currentYear = new Date().getFullYear();
    this.availableYears = Array.from({ length: 10 }, (_, i) => currentYear - i);
    this.filterForm = this._fb.group({ date: [''] });
    this.getReceivedCarpets();
  }

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    
    // Set sorting accessor for CarpetNo
    this.dataSource.sortingDataAccessor = (item: any, property: string) => {
      switch (property) {
        case 'CarpetNo':
          return item.CarpetNo.toString();
        default:
          return item[property];
      }
    };

    // Set default sorting to CarpetNo in descending order
    this.sort.sort({
      id: 'CarpetNo',
      start: 'desc',
      disableClear: false
    });

    // Update SrNo when sorting changes
    this.sort.sortChange.subscribe(() => {
      const data = this.dataSource.data;
      data.forEach((item, index) => {
        item.SrNo = index + 1;
      });
      this.dataSource.data = data;
    });
  }

  // Populate filter dropdown options from data
  populateFilterOptions(data: CarpetData[]) {
    // Extract unique weavers
    this.uniqueWeavers = [...new Set(data
      .map(item => item.Weaver)
      .filter((weaver): weaver is string => !!weaver && weaver !== 'N/A' && typeof weaver === 'string')
    )].sort();

    // Extract unique years from CDate
    const years: string[] = [];
    data.forEach(item => {
      if (item.CDate && item.CDate !== 'N/A') {
        // Handle different date formats (DD.MM.YYYY or DD/MM/YYYY)
        const dateParts = item.CDate.split(/[.\/]/);
        if (dateParts.length === 3 && dateParts[2]) {
          years.push(dateParts[2]); // Year is the third part
        }
      }
    });

    this.uniqueYears = [...new Set(years)].sort().reverse(); // Most recent years first

    console.log('🔍 Filter options populated:', {
      weavers: this.uniqueWeavers.length,
      years: this.uniqueYears.length
    });
  }

  // General search filter (searches all fields)
  applyGeneralFilter(event: Event) {
    this.generalSearchTerm = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.applyAllFilters();
  }

  // Weaver filter
  applyWeaverFilter(weaver: string) {
    this.selectedWeaver = weaver;
    this.applyAllFilters();
  }

  // Month filter
  applyMonthFilter(month: string) {
    this.selectedMonth = month;
    this.applyAllFilters();
  }

  // Year filter
  applyYearFilter(year: string) {
    this.selectedYear = year;
    this.applyAllFilters();
  }

  // Apply all filters combined
  applyAllFilters() {
    let filteredData = [...this.originalData];

    // Apply general search filter
    if (this.generalSearchTerm) {
      filteredData = filteredData.filter(item =>
        Object.values(item).some(value =>
          value && value.toString().toLowerCase().includes(this.generalSearchTerm)
        )
      );
    }

    // Apply weaver filter
    if (this.selectedWeaver) {
      filteredData = filteredData.filter(item => item.Weaver === this.selectedWeaver);
    }

    // Apply month filter
    if (this.selectedMonth) {
      filteredData = filteredData.filter(item => {
        if (item.CDate && item.CDate !== 'N/A') {
          const dateParts = item.CDate.split(/[.\/]/);
          if (dateParts.length === 3) {
            return dateParts[1] === this.selectedMonth; // Month is the second part
          }
        }
        return false;
      });
    }

    // Apply year filter
    if (this.selectedYear) {
      filteredData = filteredData.filter(item => {
        if (item.CDate && item.CDate !== 'N/A') {
          const dateParts = item.CDate.split(/[.\/]/);
          if (dateParts.length === 3) {
            return dateParts[2] === this.selectedYear; // Year is the third part
          }
        }
        return false;
      });
    }

    // Update the data source
    this.dataSource.data = filteredData;

    // Reset paginator to first page
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }

    console.log(`🔍 Filters applied: ${filteredData.length} of ${this.originalData.length} records shown`);
  }

  // Check if any filters are active
  hasActiveFilters(): boolean {
    return !!(this.selectedWeaver || this.selectedMonth || this.selectedYear || this.generalSearchTerm);
  }

  // Clear all filters
  clearAllFilters() {
    this.selectedWeaver = '';
    this.selectedMonth = '';
    this.selectedYear = '';
    this.generalSearchTerm = '';
    this.dataSource.data = [...this.originalData];

    // Reset paginator
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

availableYears: number[] = [];
editedCarpets = new Set<string>(); // Track carpets that have been edited

availableMonths = [
  { value: null, label: 'All' }, // All option
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];


formatDate(dateString: string): string {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString; // In case the date is already formatted

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}


  getReceivedCarpets() {
    this.isLoading = true;
    this.errorMessage = null;
    this.http.get('http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied').subscribe({
      next: (response: any) => {
        console.log('📦 Received carpet data:', response);

        const allData = Array.isArray(response) ? response : [];
        if (allData.length === 0) {
          console.warn('⚠️ No carpet data received.');
          this.isLoading = false;
          this.errorMessage = 'No carpet data available.';
          return;
        }

        // Sort data by CarpetNo in descending order
        allData.sort((a: any, b: any) => {
          const receiveNoA = a.receiveNo || '';
          const receiveNoB = b.receiveNo || '';
          return receiveNoB.localeCompare(receiveNoA);
        });

        // Process the data and maintain sequential SrNo
        allData.forEach((item: any, index: number) => {
          item.SrNo = index + 1;
          if (item.PaymentDt === '01.01.1970' || !item.PaymentDt) {
            item.PaymentDt = '';
          } else {
            const date = new Date(item.PaymentDt);
            if (date.getFullYear() > 1970) {
              item.PaymentDt = date.getDate().toString().padStart(2, '0') + '.' +
                             (date.getMonth() + 1).toString().padStart(2, '0') + '.' +
                             date.getFullYear();
            } else {
              item.PaymentDt = '';
            }
          }
        });

        // Weaver List
        const weaverSet = new Set<string>();
        allData.forEach(item => {
          if (item.K?.branchCode && item.weaverNumber?.name) {
            weaverSet.add(item.K.branchCode + ' - ' + item.weaverNumber.name);
          }
        });
        this.uniqueWeavers = Array.from(weaverSet).sort();

        // Fetch updates for all carpets
        const updatePromises = allData.map((entry: any) =>
          this.http
            .get(`http://localhost:2000/api/phase-four/carpetReceivedUpdates/${entry._id}`)
            .toPromise()
            .then((updates: any) => {
              console.log(`📊 Updates for carpet ${entry._id}:`, updates);

              // Handle the case where updates might be an empty array or null
              let processedUpdates = [];
              if (updates) {
                processedUpdates = Array.isArray(updates) ? updates : [updates];
              }

              // Format the payment date if it exists
              if (entry.PaymentDt && entry.PaymentDt !== '01.01.1970') {
                const date = new Date(entry.PaymentDt);
                if (date.getFullYear() > 1970) {
                  entry.PaymentDt = date.getDate().toString().padStart(2, '0') + '.' +
                                  (date.getMonth() + 1).toString().padStart(2, '0') + '.' +
                                  date.getFullYear();
                } else {
                  entry.PaymentDt = '';
                }
              } else {
                entry.PaymentDt = '';
              }

              return { entry, updates: processedUpdates };
            })
            .catch((err) => {
              console.error(`❌ Failed to fetch updates for carpet ${entry._id}:`, err);
              return { entry, updates: [] }; // Fallback to empty updates
            })
        );

        Promise.all(updatePromises)
          .then((results) => {
            const flatMapped = results.map(({ entry, updates }, i: number) => {
              const carpet = entry.issueNo || {}; // Fallback to empty object if issueNo is missing

              let areaInYard = 0;
              let areaInFeet = 0;
              let totalAreaInFeet = 0;
              let totalAreaInYard = 0;

              // Get the latest update (which now contains both edit and payment data)
              const latestUpdate = updates.length > 0 ? updates[0] : {
                // Default empty values for a new carpet with no updates
                size: null,
                deduction: null,
                paymentDt: null,
                remarks: null,
                lastUpdateType: null
              };

              // Check if we have edit or payment data in the update
              const hasEditData = latestUpdate.size || latestUpdate.deduction;
              const hasPaymentData = latestUpdate.paymentDt || latestUpdate.remarks;

              // If carpet has edit data, mark it as edited to show payment button
              if (hasEditData && entry._id) {
                this.editedCarpets.add(entry._id);
              }

              console.log(`Carpet ${entry._id} update:`, latestUpdate);
              console.log(`Has edit data: ${hasEditData}, Has payment data: ${hasPaymentData}`);

              let size = latestUpdate.size || carpet.size?.sizeInYard || '';
              if (size) {
                const parts = size.split(/[Xx]/);
                if (parts.length === 2) {
                  try {
                    const [w1, w2 = '0'] = parts[0].split('.');
                    const [l1, l2 = '0'] = parts[1].split('.');
                    const widthInInches = parseInt(w1) * 12 + parseInt(w2);
                    const lengthInInches = parseInt(l1) * 12 + parseInt(l2);
                    areaInYard = (widthInInches * lengthInInches) / 1296;
                    areaInFeet = (widthInInches * lengthInInches) / 144;
                    totalAreaInFeet = areaInFeet * (carpet.pcs || 1);
                    totalAreaInYard = areaInYard * (carpet.pcs || 1);
                  } catch (err) {
                    console.warn('⚠️ Failed to parse sizeInYard:', size, err);
                    totalAreaInFeet = 0;
                    totalAreaInYard = 0;
                  }
                }
              }

              const rateNum = parseFloat(carpet.rate) || 0;
              const area = carpet.areaIn === 'Sq.Feet' ? totalAreaInFeet : totalAreaInYard;
              const deduction = parseFloat(latestUpdate.deduction || '0');              const amount = area * rateNum - deduction;
              const tdsRate = parseFloat(entry.weaverNumber?.tds) || 0;
              const commissionRate = parseFloat(entry.weaverNumber?.commission) || 0;
              console.log('📋 commion', entry.weaverNumber?.commission);
              const tdsAmount = (tdsRate / 100) * amount;
              const commissionAmount = (commissionRate / 100) * area;
              const netAmount = amount - tdsAmount - commissionAmount;

              return {
                _id: entry._id,
                SrNo: i + 1,
                CarpetNo: entry.receiveNo || 'N/A',
                CDate: this.formatDate(entry.receivingDate),
                Weaver: entry.K?.branchCode+' - '+entry.weaverNumber?.name || 'N/A',
                IssueNo: carpet.Br_issueNo || 'N/A',
                IDate: this.customeService.convertDate(carpet.date) || 'N/A',
                Quality: carpet.quality?.quality || 'N/A',
                Design: carpet.design?.design || 'N/A',
                BorderColour: carpet.borderColour || 'N/A',
                Size: size,
                Pcs: entry.pcs || 0,
                AreaIn: carpet.areaIn || 'N/A',
                Area: carpet.areaIn === 'Sq.Feet' ? `${totalAreaInFeet.toFixed(2)} Ft` : `${totalAreaInYard.toFixed(2)} Yd`,
                Rate: rateNum,
                Amount: amount.toFixed(2),
                Deduction: deduction.toFixed(2),
                TDS_percent: tdsRate,
                Commission_percent: commissionRate,
                TDS: tdsAmount.toFixed(2),
                Commission: commissionAmount.toFixed(2),
                NetAmount: netAmount.toFixed(2),
                PaymentDt:  this.customeService.convertDate(latestUpdate.paymentDt),
                BankName: latestUpdate.bankName || '',
                ChequeNoOrRGSno: latestUpdate.chequeNoOrRGSno || '',
                PayedAmount: latestUpdate.payedAmount || '',
                Remarks: latestUpdate.remarks || '',
                branchCode: carpet.branch?.branchCode || 'N/A',
                branchName: carpet.branch?.branchName || 'N/A',
              };
            });

            console.log('📋 Mapped data for table:', flatMapped);
            this.dataSource.data = flatMapped;
            this.isLoading = false;

            if (flatMapped.length === 0) {
              this.errorMessage = 'No data to display after processing.';
            }
          })
          .catch((err) => {
            console.error('❌ Error processing updates:', err);
            this.isLoading = false;
            this.errorMessage = 'Failed to process carpet updates.';
            this.alert.error('error', 'Failed to process carpet updates.');
          });
      },
      error: (err) => {
        console.error('❌ Error fetching carpet data:', err);
        this.isLoading = false;
        this.errorMessage = 'Failed to load carpet data.';
        this.alert.error('error', 'Failed to load received carpet data.');
      }
    });
  }

  openEditModal(element: CarpetData): void {
    console.log('🔍 Opening edit modal for carpet:', element);
    console.log('🆔 Carpet ID:', element._id);
    console.log('📋 Complete carpet data:', JSON.stringify(element, null, 2));

    const dialogRef = this.dialog.open(EditCarpetModalComponent, {
      width: '500px',
      data: { ...element }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('📝 Edit modal result:', result);

        // Create a more detailed request with carpet information
        const requestData = {
          carpetReceivedId: element._id,
          size: result.Size,
          deduction: result.Deduction,
          // Add calculated fields
          area: result.Area, // Send complete area string with unit (Ft or Yd)
          amount: parseFloat(result.Amount) || 0,
          tds: parseFloat(result.TDS) || 0,
          commission: parseFloat(result.Commission) || 0,
          netAmount: parseFloat(result.NetAmount) || 0,
          updateType: 'edit',
          // Include additional carpet data for reference
          carpetInfo: {
            carpetNo: element.CarpetNo,
            quality: element.Quality,
            design: element.Design,
            weaver: element.Weaver,
            issueNo: element.IssueNo
          }
        };

        console.log('🔄 Sending edit request:', requestData);
        console.log('🆔 Carpet ID being sent:', element._id);
        console.log('🧵 Carpet ID type:', typeof element._id);

        // Log the raw request data as it will be sent to the server
        console.log('📦 Raw request data:', JSON.stringify(requestData));

        this.http
          .post('http://localhost:2000/api/phase-four/carpetReceivedUpdates', requestData)
          .subscribe({
            next: (response) => {
              console.log('✅ Edit response received:', response);

              // Get the history for this carpet
              this.http
                .get(`http://localhost:2000/api/phase-four/carpetReceivedUpdates/${element._id}/history/edit`)
                .subscribe({
                  next: (historyData) => {
                    console.log('📜 Edit history data:', historyData);
                    console.log('📜 History structure:', JSON.stringify(historyData, null, 2));
                  },
                  error: (histErr) => {
                    console.error('❌ Error fetching edit history:', histErr);
                  }
                });

              // Update the element with the result from the dialog
              Object.assign(element, result);
              this.updateCalculations(element);

              // Mark this carpet as edited to enable payment button
              if (element._id) {
                this.editedCarpets.add(element._id);
              }

              this.dataSource.data = [...this.dataSource.data];
              this.alert.success('success', 'Carpet data updated successfully.');
            },
            error: (err) => {
              console.error('❌ Error saving edit:', err);
              console.error('❌ Error details:', err.error || err.message || err);
              this.alert.error('error', 'Failed to save carpet data.');
            }
          });
      } else {
        console.log('🚫 Edit modal closed without changes');
      }
    });
  }

  // Check if a carpet has been edited (to show payment button)
  isCarpetEdited(carpetId: string | undefined): boolean {
    return carpetId ? this.editedCarpets.has(carpetId) : false;
  }

  openPaymentModal(element: CarpetData): void {
    console.log('💰 Opening payment modal for carpet:', element);
    
    // Only pass PaymentDt if it's not the default date
    const paymentDt = element.PaymentDt === '01.01.1970' ? null : element.PaymentDt;
    
    const dialogRef = this.dialog.open(PaymentModalComponent, {
      width: '550px',
      data: {
        _id: element._id,
        PaymentDt: paymentDt,
        Remarks: element.Remarks || '',
        CarpetNo: element.CarpetNo || '',
        BankName: element.BankName || '',
        ChequeNoOrRGSno: element.ChequeNoOrRGSno || '',
        PayedAmount: element.PayedAmount || '',
        NetAmount: element.NetAmount ,
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('💵 Payment modal result:', result);

        // Create a more detailed request with carpet information
        const requestData = {
          carpetReceivedId: element._id,
          paymentDt: result.PaymentDt,
          remarks: result.Remarks,
          bankName: result.BankName,
          chequeNoOrRGSno: result.ChequeNoOrRGSno,
          payedAmount: result.PayedAmount,
          updateType: 'payment',
          carpetInfo: {
            carpetNo: element.CarpetNo,
            quality: element.Quality,
            design: element.Design,
            weaver: element.Weaver,
             NetAmount:element.NetAmount,
            issueNo: element.IssueNo
          }
        };

        // After successful payment update, refresh the data
        this.http.post('http://localhost:2000/api/phase-four/carpetReceivedUpdates', requestData).subscribe({
          next: (response) => {
            console.log('✅ Payment update successful:', response);
            // Refresh the data by calling getReceivedCarpets
            this.getReceivedCarpets();
          },
          error: (error) => {
            console.error('❌ Payment update failed:', error);
          }
        });
      }
    });
  }



  updateSize(element: CarpetData) {
    if (element.Size && element.AreaIn) {
      const parts = element.Size.split(/[Xx]/);
      if (parts.length === 2) {
        const [w1, w2 = '0'] = parts[0].trim().split('.');
        const [l1, l2 = '0'] = parts[1].trim().split('.');
        const widthInInches = parseInt(w1) * 12 + parseInt(w2);
        const lengthInInches = parseInt(l1) * 12 + parseInt(l2);
        let area = 0;
        if (element.AreaIn === 'Sq.Feet') {
          area = (widthInInches * lengthInInches) / 144;
          element.Area = `${(area * (element.Pcs || 1)).toFixed(2)} Ft`;
        } else if (element.AreaIn === 'Sq.Yard') {
          area = (widthInInches * lengthInInches) / 1296;
          element.Area = `${(area * (element.Pcs || 1)).toFixed(2)} Yd`;
        }
        this.updateCalculations(element, area * (element.Pcs || 1));
      }
    }
  }

  updateCalculations(element: CarpetData, area?: number) {    const calcArea = area || parseFloat(element.Area.split(' ')[0]) || 0;
    const rate = element.Rate || 0;
    const deduction = parseFloat(element.Deduction);
    const tdsPercent = element.TDS_percent || 0;
    const commissionPercent = element.Commission_percent || 0;

    const amount = calcArea * rate - deduction;
    element.Amount = amount.toFixed(2);

    const tds = (amount * tdsPercent) / 100;
    element.TDS = tds.toFixed(2);

    const commission = (calcArea * commissionPercent) / 100;
    element.Commission = commission.toFixed(2);

    element.NetAmount = (amount - tds - commission).toFixed(2);
  }

  // Add a delete handler
  onDeleteCarpet(element: CarpetData) {
    this.alert.confirm({
      title: 'Are you sure?',
      text: 'You want to delete this carpet entry?',
      icon: 'warning',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    }).then((result: any) => {
      if (result.isConfirmed) {
        this.http.delete(`http://localhost:2000/api/phase-four/carpetReceived/carpetRecevied/${element._id}`)
          .subscribe({
            next: () => {
              this.alert.success('Success', 'Carpet entry deleted successfully.');
              // Remove from table
              this.dataSource.data = this.dataSource.data.filter(item => item._id !== element._id);
            },
            error: (err) => {
              this.alert.error('Error', 'Failed to delete carpet entry.');
              console.error('Delete error:', err);
            }
          });
      }
    });
  }
}