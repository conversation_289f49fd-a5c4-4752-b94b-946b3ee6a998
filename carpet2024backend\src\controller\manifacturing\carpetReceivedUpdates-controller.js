const carpetReceivedUpdatesService = require("../../services/manifacturing/carpetReceivedUpdates-service");
const CarpetReceived = require("../../model/phase-4/carpetReceived");
const CarpetReceivedUpdate = require("../../model/phase-4/carpetReceivedUpdates");

const createCarpetReceivedUpdate = async (req, res) => {
  try {
    const {
      carpetReceivedId,
      updateType,
      carpetInfo,
      ...rest // This will capture all other fields from the form
    } = req.body;

    console.log('Received update request:', req.body);
    console.log('Carpet ID:', carpetReceivedId);
    console.log('Update type:', updateType);

    if (carpetInfo) {
      console.log('Additional carpet info received:', carpetInfo);
    }

    // Fetch the complete carpet data
    const carpetData = await CarpetReceived.findById(carpetReceivedId)
      .populate('issueNo')
      .populate({
        path: 'issueNo',
        populate: [
          { path: 'quality' },
          { path: 'design' },
          { path: 'size' }
        ]
      });

    if (!carpetData) {
      return res.status(404).json({ message: `Carpet with ID ${carpetReceivedId} not found` });
    }

    console.log('Found complete carpet data:', carpetData);

    // Create or update the record
    const update = await carpetReceivedUpdatesService.createCarpetReceivedUpdate({
      carpetReceivedId,
      carpetData, // Store the complete carpet data
      updateType,
      carpetInfo,
      ...rest, // Pass all other fields (border fields, etc.)
      updatedAt: new Date() // Ensure we have a timestamp
    });

    // Return a more detailed response
    const response = {
      _id: update._id,
      carpetReceivedId: update.carpetReceivedId,
      lastUpdateType: update.lastUpdateType,
      // Include all fields regardless of update type for convenience
      size: update.size,
      deduction: update.deduction,
      area: update.area,
      rate: update.rate,
      amount: update.amount,
      tds: update.tds,
      commission: update.commission,
      netAmount: update.netAmount,
      paymentDt: update.paymentDt,
      remarks: update.remarks,
      updatedAt: update.updatedAt,
      lastEditAt: update.lastEditAt,
      lastPaymentAt: update.lastPaymentAt,
      // Include history counts
      editHistoryCount: update.editHistory ? update.editHistory.length : 0,
      paymentHistoryCount: update.paymentHistory ? update.paymentHistory.length : 0,
      // Include a message about empty entries
      cleanedEmptyEntries: true
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error in createCarpetReceivedUpdate:', error);
    res.status(500).json({ message: error.message });
  }
};

const getUpdatesByCarpetReceivedId = async (req, res) => {
  try {
    const updates = await carpetReceivedUpdatesService.getUpdatesByCarpetReceivedId(req.params.carpetReceivedId);
    res.status(200).json(updates);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getHistoryByCarpetAndType = async (req, res) => {
  try {
    const { carpetReceivedId, updateType } = req.params;
    const history = await carpetReceivedUpdatesService.getHistoryByCarpetAndType(carpetReceivedId, updateType);
    res.status(200).json(history);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Utility endpoint to clean up all existing records
const cleanupAllRecords = async (_, res) => {
  try {
    console.log('Starting cleanup of all carpet received update records');

    // Get all records
    const allRecords = await CarpetReceivedUpdate.find({});
    console.log(`Found ${allRecords.length} records to clean`);

    let cleanedCount = 0;

    // Process each record
    for (const record of allRecords) {
      // Clean up payment history
      if (record.paymentHistory && record.paymentHistory.length > 0) {
        // First filter out completely empty entries
        const originalLength = record.paymentHistory.length;
        record.paymentHistory = record.paymentHistory.filter(entry =>
          entry.paymentDt || entry.remarks
        );

        // Then remove null fields from remaining entries
        record.paymentHistory.forEach(entry => {
          // Remove any size or calculated fields that might exist in older records
          if (entry.size) delete entry.size;
          if (entry.area) delete entry.area;
          if (entry.amount) delete entry.amount;
          if (entry.tds) delete entry.tds;
          if (entry.commission) delete entry.commission;
          if (entry.netAmount) delete entry.netAmount;

          // Only keep payment-related fields
          if (entry.paymentDt === null) delete entry.paymentDt;
          if (entry.remarks === null) delete entry.remarks;
        });

        if (originalLength !== record.paymentHistory.length) {
          console.log(`Cleaned payment history for record ${record._id}, removed ${originalLength - record.paymentHistory.length} entries`);
        }
      }

      // Clean up edit history
      if (record.editHistory && record.editHistory.length > 0) {
        // First filter out completely empty entries
        const originalLength = record.editHistory.length;
        record.editHistory = record.editHistory.filter(entry =>
          entry.size || entry.deduction
        );

        // Then remove null fields from remaining entries
        record.editHistory.forEach(entry => {
          if (entry.size === null) delete entry.size;
          if (entry.deduction === null) delete entry.deduction;
          if (entry.area === null) delete entry.area;
          if (entry.amount === null) delete entry.amount;
          if (entry.tds === null) delete entry.tds;
          if (entry.commission === null) delete entry.commission;
          if (entry.netAmount === null) delete entry.netAmount;
          if (entry.paymentDt === null) delete entry.paymentDt;
          if (entry.remarks === null) delete entry.remarks;
        });

        if (originalLength !== record.editHistory.length) {
          console.log(`Cleaned edit history for record ${record._id}, removed ${originalLength - record.editHistory.length} entries`);
        }
      }

      // Save the cleaned record
      await record.save();
      cleanedCount++;
    }

    console.log(`Cleanup complete. Processed ${cleanedCount} records.`);
    res.status(200).json({
      message: `Cleanup complete. Processed ${cleanedCount} records.`,
      recordsProcessed: cleanedCount
    });
  } catch (error) {
    console.error('Error in cleanupAllRecords:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get all carpet received updates
const getAllCarpetReceivedUpdates = async (req, res) => {
  try {
    console.log('Controller: Getting all carpet received updates');
    const updates = await carpetReceivedUpdatesService.getAllCarpetReceivedUpdates();
    console.log(`Controller: Found ${updates.length} carpet received updates`);
    res.status(200).json(updates);
  } catch (error) {
    console.error('Error in getAllCarpetReceivedUpdates:', error);
    res.status(500).json({ message: error.message });
  }
};

const addCarpetBorderSizeHistory = async (req, res) => {
  try {
    const { carpetReceivedId } = req.params;
    const borderData = req.body;
    const entry = await carpetReceivedUpdatesService.addCarpetBorderSizeHistory(carpetReceivedId, borderData);
    res.status(201).json(entry);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getCarpetBorderSizeHistory = async (req, res) => {
  try {
    const { carpetReceivedId } = req.params;
    const history = await carpetReceivedUpdatesService.getCarpetBorderSizeHistory(carpetReceivedId);
    res.status(200).json(history);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createCarpetReceivedUpdate,
  getUpdatesByCarpetReceivedId,
  getHistoryByCarpetAndType,
  cleanupAllRecords,
  getAllCarpetReceivedUpdates,
  addCarpetBorderSizeHistory,
  getCarpetBorderSizeHistory
};