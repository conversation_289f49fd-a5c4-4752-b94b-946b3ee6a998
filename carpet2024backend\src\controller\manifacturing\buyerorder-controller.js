const buyerOrderService = require('../../services/manifacturing/buyerOrder-service');

class BuyerOrderController {
  async createBuyerOrder(req, res) {
    try {
      const buyerOrder = await buyerOrderService.createBuyerOrder(req.body);
      res.status(201).json(buyerOrder);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getBuyerOrderById(req, res) {
    try {
      const buyerOrder = await buyerOrderService.getBuyerOrderById(req.params.id);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder not found' });
      }
      res.status(200).json(buyerOrder);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getAllBuyerOrders(req, res) {
    try {
      const buyerOrders = await buyerOrderService.getAllBuyerOrders();
      res.status(200).json(buyerOrders);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async updateBuyerOrder(req, res) {
    try {
      const buyerOrder = await buyerOrderService.updateBuyerOrder(req.params.id, req.body);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder not found' });
      }

      // Check if response contains warning
      if (buyerOrder.success === false && buyerOrder.warning === true) {
        return res.status(400).json(buyerOrder);
      }

      res.status(200).json(buyerOrder);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async deleteBuyerOrder(req, res) {
    try {
      const buyerOrder = await buyerOrderService.deleteBuyerOrder(req.params.id);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder already issued' });
      }
      return res.status(200).json({ message: 'BuyerOrder deleted successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async deleteItem(req, res) {
    try {
      const buyerOrder = await buyerOrderService.deleteItem(req.params.id);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder item already issued' });
      }
      return res.status(200).json({ message: 'BuyerOrder item deleted successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async updateBuyerOrderField(req, res) {
    try {
      const { field, value } = req.body;
      const buyerOrder = await buyerOrderService.updateBuyerOrderField(req.params.id, field, value);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder not found' });
      }
      res.status(200).json(buyerOrder);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async deleteBuyerOrderField(req, res) {
    try {
      const { field } = req.body;
      const buyerOrder = await buyerOrderService.deleteBuyerOrderField(req.params.id, field);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder not found' });
      }
      res.status(200).json(buyerOrder);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async calculateFields(req, res) {
    try {
      const buyerOrder = await buyerOrderService.getBuyerOrderById(req.params.id);
      if (!buyerOrder) {
        return res.status(404).json({ message: 'BuyerOrder not found' });
      }
      const calculatedFields = {
        totalIssue: buyerOrderService.calculateTotalIssued(buyerOrder),
        totalCancel: buyerOrderService.calculateTotalCanceled(buyerOrder),
        totalStock: buyerOrderService.calculateTotalStock(buyerOrder),
        totalOrdered: buyerOrderService.calculateTotalOrdered(buyerOrder)
      };
      res.status(200).json(calculatedFields);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new BuyerOrderController();
