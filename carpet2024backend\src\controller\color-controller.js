const colorService = require('../services/color-service');

class ColorController {
  async createColor(req, res) {
    try {
      const { initialColor, newColor, colorCode, companyColorCode, remark, quality } = req.body;
      const color = await colorService.createColor({ initialColor, newColor, colorCode, companyColorCode, remark, quality });
      res.status(201).json(color);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getColorById(req, res) {
    try {
      const colorId = req.params.id;
      const color = await colorService.getColorById(colorId);
      res.status(200).json(color);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllColors(req, res) {
    try {
      const colors = await colorService.getAllColors();
      res.status(200).json(colors);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateColor(req, res) {
    try {
      const colorId = req.params.id;
      const {  newColor, colorCode,companyColorCode ,remark, quality } = req.body;
      const updatedColor = await colorService.updateColor(colorId, {  newColor,companyColorCode, colorCode, remark, quality });
      res.status(200).json(updatedColor);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteColor(req, res) {
    try {
      const colorId = req.params.id;
      const deletedColor = await colorService.deleteColor(colorId);
      res.status(200).json(deletedColor);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new ColorController();
