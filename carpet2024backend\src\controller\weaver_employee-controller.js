const weaverEmployeeService = require('../services/weaver_employess-service');
const uploadFiles = require('../middleware/multer-middleware'); // Import the modified upload middleware
const multer = require('multer');
const path = require('path');

const createEmployee = async (req, res) => {
  try {
    uploadFiles(req, res, async function (err) {
      if (err instanceof multer.MulterError) {
        return res.status(400).send({ message: "Error uploading file", error: err });
      } else if (err) {
        return res.status(500).send({ message: "Unknown error uploading file", error: err });
      }

     // console.log('Files uploaded:', req.files); // Log uploaded files

      if (!req.files) {
        return res.status(400).send({ message: "No files uploaded" });
      }

      const files = req.files.reduce((acc, file) => {
        const fileName = path.basename(file.path);
        acc[file.fieldname] = `/public/all-files/${fileName}`;
        return acc;
      }, {});

     

      const employeeData = {
        branch:req.body.branch,
        groupName: req.body.groupName,
        name: req.body.name,
        address: req.body.address,
        zipcode: req.body.zipcode,
        country: req.body.country,
        contactNo: req.body.contactNo,
        ifscCode:req.body.ifscCode,
        bankAccountNo:req.body.bankAccountNo,
        tds:req.body.tds,
        commission:req.body.commission,
        bankName:req.body.bankName,
        aadhaarDetails: {
          aadhaarNo: req.body.aadhaarNo,
          aadhaarFile: files.aadhaarFile // Use the formatted path
        },
        panDetails: {
          panNo: req.body.panNo,
          panFile: files.panFile // Use the formatted path
        },
        epfoDetails: {
          epfoNo: req.body.epfoNo,
          epfoFile: files.epfoFile // Use the formatted path
        },
        esicDetails: {
          esicNo: req.body.esicNo,
          esicFile: files.esicFile // Use the formatted path
        },
        emailId: req.body.emailId,
        password: req.body.password
      };

      const newEmployee = await weaverEmployeeService.createEmployee(employeeData);
      res.status(201).send(newEmployee);
    });
  } catch (error) {
   // console.error("Error creating employee:", error);
    res.status(400).send({ message: "Error creating employee", error });
  }
};

const getEmployees = async (req, res) => {
  try {
    const employees = await weaverEmployeeService.getEmployees();
    res.status(200).send(employees);
  } catch (error) {
    //console.error("Error fetching employees:", error);
    res.status(500).send({ message: "Error fetching employees", error });
  }
};

const getEmployeeById = async (req, res) => {
  try {
    const employee = await weaverEmployeeService.getEmployeeById(req.params.id);
    if (!employee) {
      return res.status(404).send({ message: "Employee not found" });
    }
    res.status(200).send(employee);
  } catch (error) {
   // console.error("Error fetching employee:", error);
    res.status(500).send({ message: "Error fetching employee", error });
  }
};

const updateEmployee = async (req, res) => {
  try {
    uploadFiles(req, res, async function (err) {
      if (err instanceof multer.MulterError) {
        return res.status(400).send({ message: "Error uploading file", error: err });
      } else if (err) {
        return res.status(500).send({ message: "Unknown error uploading file", error: err });
      }

      //console.log('Files uploaded:', req.files); // Log uploaded files

      if (!req.files) {
        return res.status(400).send({ message: "No files uploaded" });
      }

      const files = req.files.reduce((acc, file) => {
        const fileName = path.basename(file.path);
        acc[file.fieldname] = `/public/all-files/${fileName}`;
        return acc;
      }, {});

     // console.log('Processed files:', files); // Log processed file paths

      const employeeData = {
        branch:req.body.branch,
        groupName: req.body.groupName,
        name: req.body.name,
        address: req.body.address,
        zipcode: req.body.zipcode,
        country: req.body.country,
        contactNo: req.body.contactNo,
        ifscCode:req.body.ifscCode,
        bankAccountNo:req.body.bankAccountNo,
        tds:req.body.tds,
        commission:req.body.commission,
        bankName:req.body.bankName,
        aadhaarDetails: {
          aadhaarNo: req.body.aadhaarNo,
          aadhaarFile: files.aadhaarFile // Use the formatted path
        },
        panDetails: {
          panNo: req.body.panNo,
          panFile: files.panFile // Use the formatted path
        },
        epfoDetails: {
          epfoNo: req.body.epfoNo,
          epfoFile: files.epfoFile // Use the formatted path
        },
        esicDetails: {
          esicNo: req.body.esicNo,
          esicFile: files.esicFile // Use the formatted path
        },
        emailId: req.body.emailId,
        password: req.body.password
      };

      const updatedEmployee = await weaverEmployeeService.updateEmployee(req.params.id, employeeData);
      if (!updatedEmployee) {
        return res.status(404).send({ message: "Employee not found" });
      }
      res.status(200).send(updatedEmployee);
    });
  } catch (error) {
    //console.error("Error updating employee:", error);
    res.status(400).send({ message: "Error updating employee", error });
  }
};

const deleteEmployee = async (req, res) => {
  try {
    const employee = await weaverEmployeeService.deleteEmployee(req.params.id);
    if (!employee) {
      return res.status(404).send({ message: "Employee not found" });
    }
    res.status(200).send({ message: "Employee deleted successfully" });
  } catch (error) {
   // console.error("Error deleting employee:", error);
    res.status(500).send({ message: "Error deleting employee", error });
  }
};

module.exports = {
  createEmployee,
  getEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee
};
