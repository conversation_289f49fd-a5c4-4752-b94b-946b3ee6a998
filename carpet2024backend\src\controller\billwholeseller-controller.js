const BillForWholesellerService = require("../services/billwholeseller-service");
const {CreateChallan} = require("../model/phase-2/createChallan");
class BillForWholesellerController {
  constructor() {
    this.billForWholesellerService = new BillForWholesellerService();
  }

  async createBill(req, res) {
    try {
      const billData = req.body;
      let flag = 0;
      const updatePromises = billData.challanNo.map(async (element) => {
        console.log(element);
        let updated = await CreateChallan.updateOne(
          { challanNo: element },
          { $set: { isBillCreated: true } }          
        );

        if (updated){
          flag = 1;
        }
      });

      await Promise.all(updatePromises);
      if (flag != 1) {
        res.status(400).json({ message: "Bill has been not saved !" });
      }
      const newBill = await this.billForWholesellerService.createBill(billData);
      res.status(201).json(newBill);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getAllBills(req, res) {
    try {
      const bills = await this.billForWholesellerService.getAllBills();
      res.json(bills);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getBillById(req, res) {
    try {
      const id = req.params.id;
      const bill = await this.billForWholesellerService.getBillById(id);
      if (!bill) {
        res.status(404).json({ message: "Bill not found" });
      } else {
        res.json(bill);
      }
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async updateBill(req, res) {
    try {
      const id = req.params.id;
      const billData = req.body;
      console.log("ghgjgh",billData);
      const updatedBill = await this.billForWholesellerService.updateBill( id, billData );
      res.json(updatedBill);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async deleteBill(req, res) {
    try {
      const id = req.params.id;
   let data=   await this.billForWholesellerService.deleteBill(id);
   if (data.acknowledged === true && data.modifiedCount == 1 ) {
    return res.status(200).send({
      success:true,
      message:'bill data has been deleted'
    });
  }   else{
   return res.status(204).send({
      success:false,
      message:'something went wrong'
    });
  }

  //  res.status(204).send();
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getCarpetDetails(req,res){
    try {
      const {carpetNo, toDate, fromDate} = req.body;

      const carpetDetails = await this.billForWholesellerService.getCarpetDetails(req.body);
      if (!carpetDetails) {
    return   res.status(404).json({ message: "Carpet Details not found" });
      } else {
        return res.json(carpetDetails);
      }
    } catch (error) {
      return res.status(400).json({ message: error.message });
    }
  }
}

module.exports = BillForWholesellerController;
