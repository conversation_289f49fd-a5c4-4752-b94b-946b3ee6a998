const express = require('express');
const router = express.Router();
const CarpetReceived = require('../../models/CarpetReceived');

// Drop the existing index if it exists (do this only once)
CarpetReceived.collection.dropIndex('issueNo_1').catch(() => {});

router.post('/carpetReceived', async (req, res) => {
    try {
        // Add timestamp if not present
        if (!req.body.timestamp) {
            req.body.timestamp = new Date().getTime();
        }

        const carpetData = new CarpetReceived(req.body);
        const savedData = await carpetData.save();
        res.status(201).json(savedData);
    } catch (error) {
        console.error('Error saving carpet:', error);
        res.status(500).json({ 
            message: 'Error saving carpet piece', 
            error: error.message 
        });
    }
});

module.exports = router;
