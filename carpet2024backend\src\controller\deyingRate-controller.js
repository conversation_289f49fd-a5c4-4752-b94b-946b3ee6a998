// controllers/deyingRateController.js
const deyingRateService = require('../services/deyingRate-service');

async function createDeyingRate(req, res) {
  try {
    const data = req.body;
    const createdDeyingRate = await deyingRateService.createDeyingRate(data);
    res.status(201).json(createdDeyingRate);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function getDeyingRateById(req, res) {
  try {
    const id = req.params.id;
    const deyingRate = await deyingRateService.getDeyingRateById(id);
    if (!deyingRate) {
      return res.status(404).json({ message: 'Deying rate not found' });
    }
    res.json(deyingRate);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function getAllDeyingRates(req, res) {
  try {
    const deyingRates = await deyingRateService.getAllDeyingRates();
    res.json(deyingRates);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function updateDeyingRate(req, res) {
  try {
    const id = req.params.id;
    const data = req.body;
    const updatedDeyingRate = await deyingRateService.updateDeyingRate(id, data);
    res.json(updatedDeyingRate);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function deleteDeyingRate(req, res) {
  try {
    const id = req.params.id;
    await deyingRateService.deleteDeyingRate(id);
    res.status(204).end();
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

module.exports = {
  createDeyingRate,
  getDeyingRateById,
  getAllDeyingRates,
  updateDeyingRate,
  deleteDeyingRate
};
