const AddDesignService = require('../services/design-service');
const path = require('path');

class AddDesignController {
  constructor() {
    this.addDesignService = new AddDesignService();
  }

  async createDesign(req, res) {
    try {
      const { productQuality, design, ground, border, noOfcolour } = req.body;
      let { colourLagats } = req.body;

      // Parse colourLagats if it is a string
      if (typeof colourLagats === 'string') {
        colourLagats = JSON.parse(colourLagats);
      }

      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename; // Use only the file name
      }

      const newDesign = await this.addDesignService.createDesign({
        productQuality,
        design,
        ground,
        border,
        uploadedFile: fileName, // Store the URL path for the file
        noOfcolour,
        colourLagats
      });

      res.status(201).json(newDesign);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async getAllDesigns(req, res) {
    try {
      const designs = await this.addDesignService.getAllDesigns();
      res.json(designs);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getDesignById(req, res) {
    try {
      const designId = req.params.id;
      const design = await this.addDesignService.getDesignById(designId);
      res.json(design);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async updateDesign(req, res) {
    try {
      const designId = req.params.id;
      const { productQuality, design, ground, border, noOfcolour } = req.body;
      let { colourLagats } = req.body;

      // Parse colourLagats if it is a string
      if (typeof colourLagats === 'string') {
        colourLagats = JSON.parse(colourLagats);
      }

      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename; // Get the filename of the uploaded file from req.files
      }

      const designData = {
        productQuality,
        design,
        ground,
        border,
        uploadedFile: fileName, // Store the URL path for the file
        noOfcolour,
        colourLagats
      };

      const updatedDesign = await this.addDesignService.updateDesign(designId, designData);
      res.json(updatedDesign);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  async deleteDesign(req, res) {
    try {
      const designId = req.params.id;
      await this.addDesignService.deleteDesign(designId);
      res.status(204).send();
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = AddDesignController;
