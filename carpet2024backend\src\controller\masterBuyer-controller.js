// controllers/buyerController.js
const buyerService = require('../services/masterBuyer-service');

async function getAllBuyers(req, res) {
    try {
        const buyers = await buyerService.getAllBuyers();
        res.json(buyers);
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

async function getBuyerById(req, res) {
    try {
        const id = req.params.id;
        const buyer = await buyerService.getBuyerById(id);
        res.json(buyer);
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

async function createBuyer(req, res) {
    try {
        const buyerData = req.body;
        const newBuyer = await buyerService.createBuyer(buyerData);
        res.json(newBuyer);
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

async function updateBuyer(req, res) {
    try {
        const id = req.params.id;
        const newData = req.body;
        const updatedBuyer = await buyerService.updateBuyer(id, newData);
        res.json(updatedBuyer);
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

async function deleteBuyer(req, res) {
    try {
        const id = req.params.id;
        await buyerService.deleteBuyer(id);
        res.json({ message: 'Buyer deleted successfully' });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
}

module.exports = {
    getAllBuyers,
    getBuyerById,
    createBuyer,
    updateBuyer,
    deleteBuyer
};
