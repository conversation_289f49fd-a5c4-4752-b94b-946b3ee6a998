const AddqualityService = require('../services/quality-service');

class AddqualityController {
    constructor() {
        this.addqualityService = new AddqualityService();
    }

    async createQuality(req, res) {
        try {
            const qualityData = req.body;
            const newQuality = await this.addqualityService.createQuality(qualityData);
            res.status(201).json(newQuality);
        } catch (error) {
            res.status(400).json({ message: error.message });
        }
    }

    async getAllQualities(req, res) {
        try {
            const qualities = await this.addqualityService.getAllQualities();
            res.json(qualities);
        } catch (error) {
            res.status(400).json({ message: error.message });
        }
    }

    async getQualityById(req, res) {
        try {
            const qualityId = req.params.id;
            const quality = await this.addqualityService.getQualityById(qualityId);
            res.json(quality);
        } catch (error) {
            res.status(400).json({ message: error.message });
        }
    }

    async updateQuality(req, res) {
        try {
            const qualityId = req.params.id;
            const qualityData = req.body;
            const updatedQuality = await this.addqualityService.updateQuality(qualityId, qualityData);
            res.json(updatedQuality);
        } catch (error) {
            res.status(400).json({ message: error.message });
        }
    }

    async deleteQuality(req, res) {
        try {
            const qualityId = req.params.id;
            await this.addqualityService.deleteQuality(qualityId);
            res.status(204).send();
        } catch (error) {
            res.status(400).json({ message: error.message });
        }
    }
}

module.exports = AddqualityController;
