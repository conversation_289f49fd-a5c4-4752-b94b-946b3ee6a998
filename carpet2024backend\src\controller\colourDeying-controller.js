const colourcodeDeyingService = require('../services/colourDeying-service');

const createColourcodeDeying = async (req, res) => {
  try {
    const colourcodeDeyingData = req.body;
    const newColourcodeDeying = await colourcodeDeyingService.createColourcodeDeying(colourcodeDeyingData);
    res.status(201).json(newColourcodeDeying);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllColourcodeDeyings = async (req, res) => {
  try {
    const colourcodeDeyings = await colourcodeDeyingService.getAllColourcodeDeyings();
    res.status(200).json(colourcodeDeyings);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getColourcodeDeyingById = async (req, res) => {
  try {
    const colourcodeDeying = await colourcodeDeyingService.getColourcodeDeyingById(req.params.id);
    if (!colourcodeDeying) {
      return res.status(404).json({ message: "Colourcode Deying not found" });
    }
    res.status(200).json(colourcodeDeying);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateColourcodeDeying = async (req, res) => {
  try {
    const { id } = req.params;
    const colourcodeDeyingData = req.body;
    const updatedColourcodeDeying = await colourcodeDeyingService.updateColourcodeDeying(id, colourcodeDeyingData);
    res.status(200).json(updatedColourcodeDeying);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteColourcodeDeying = async (req, res) => {
  try {
    const { id } = req.params;
    await colourcodeDeyingService.deleteColourcodeDeying(id);
    res.status(200).json({ message: "Colourcode Deying deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateField = async (req, res) => {
  try {
    const { id } = req.params;
    const { chemicalIndex, field, value } = req.body;
    const updatedColourcodeDeying = await colourcodeDeyingService.updateField(id, chemicalIndex, field, value);
    res.status(200).json(updatedColourcodeDeying);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteField = async (req, res) => {
  try {
    const { id } = req.params;
    const { chemicalIndex, field } = req.body;
    const updatedColourcodeDeying = await colourcodeDeyingService.deleteField(id, chemicalIndex, field);
    res.status(200).json(updatedColourcodeDeying);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createColourcodeDeying,
  getAllColourcodeDeyings,
  getColourcodeDeyingById,
  updateColourcodeDeying,
  deleteColourcodeDeying,
  updateField,
  deleteField
};
