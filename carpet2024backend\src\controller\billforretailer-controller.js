const BillForRetailerService = require("../services/billforretailer-service");
const {CreateChallan} = require("../model/phase-2/createChallan");

class BillForRetailerController {
  constructor() {
    this.billForRetailerService = new BillForRetailerService();
  }

  async createBill(req, res) {
    try {
      const billData = req.body;
      let flag = 0;
      const updatePromises = billData.challanNo.map(async (element) => {
        console.log(element);
        let updated = await CreateChallan.updateOne(
          { challanNo: element },
          { $set: { isBillCreated: true } }
          
        );

        if (updated){
          flag = 1;
        }
      });

      await Promise.all(updatePromises);
      if (flag != 1) {
        res.status(400).json({ message: "Bill has been not saved !" });
      }
      const newBill = await this.billForRetailerService.createBill(billData);
      res.status(201).json(newBill);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getAllBills(req, res) {
    try {
      const bills = await this.billForRetailerService.getAllBills();
      res.json(bills);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getBillById(req, res) {
    try {
      const id = req.params.id;
      const bill = await this.billForRetailerService.getBillById(id);
      if (!bill) {
        res.status(404).json({ message: "Bill not found" });
      } else {
        res.json(bill);
      }
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async updateBill(req, res) {
    try {
      const id = req.params.id;
      const billData = req.body;
      const updatedBill = await this.billForRetailerService.updateBill(
        id,
        billData
      );
      res.json(updatedBill);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async deleteBill(req, res) {
    try {
      const id = req.params.id;
      let data=   await this.billForRetailerService.deleteBill(id);
      if (data.acknowledged === true && data.modifiedCount == 1 ) {
        return res.status(200).send({
          success:true,
          message:'bill data has been deleted'
        });
      }   else{
        return res.status(204).send({
          success:false,
          message:'something went wrong'
        });
      }
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = BillForRetailerController;
