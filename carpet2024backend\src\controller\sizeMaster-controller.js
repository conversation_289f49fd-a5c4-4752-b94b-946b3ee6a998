const sizeService = require('../services/sizeMaster-service');
const path = require('path');

class SizeController {
  async createSize(req, res) {
    try {
      const { sizeInYard, areaInYard, areaInfeet, khapSize, sizeinMeter, sqMeter, srNo } = req.body;
      
     
      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename; // Use only the file name
      }
      const size = await sizeService.createSize({ 
        sizeInYard, 
        areaInYard, 
        areaInfeet, 
        khapSize, 
        sizeinMeter, 
        sqMeter, 
        srNo,
        uploadedFile: fileName // Add the file URL to the payload
      });

      res.status(201).json(size);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getSizeById(req, res) {
    try {
      const size = await sizeService.getSizeById(req.params.id);
      if (!size) {
        return res.status(404).json({ error: 'Size not found' });
      }
      res.json(size);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getAllSizes(req, res) {
    try {
      const sizes = await sizeService.getAllSizes();
      res.json(sizes);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async updateSize(req, res) {
    try {
      const { sizeInYard, areaInYard, areaInfeet, khapSize, sizeinMeter, sqMeter, srNo } = req.body;

      let fileName = '';
      if (req.files && req.files.length > 0) {
        fileName = req.files[0].filename; // Get the filename of the uploaded file from req.files
      }
      const size = await sizeService.updateSize(req.params.id, { 
        sizeInYard, 
        areaInYard, 
        areaInfeet, 
        khapSize, 
        sizeinMeter, 
        sqMeter, 
        srNo,
        uploadedFile: fileName // Add the file URL to the payload
      });

      if (!size) {
        return res.status(404).json({ error: 'Size not found' });
      }
      res.json(size);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async deleteSize(req, res) {
    try {
      const size = await sizeService.deleteSize(req.params.id);
      if (!size) {
        return res.status(404).json({ error: 'Size not found' });
      }
      res.json({ message: 'Size deleted successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new SizeController();
