const userService = require('../services/user-service');

class UserController {
  async createUser(req, res) {
    try {
      //console.log(req.body);
      const {name,email,password} = req.body;
      const user = await userService.createUser({name,email,password});
      res.status(201).json(user);
    } catch (error) {
      //console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }

  }
  async loginUser(req, res) {
    const { email, password } = req.body;
    try {
      
      const token = await userService.loginUser(email, password);
      res.status(200).json(token);
    } catch (error) {
      res.status(401).json({ message: 'Authentication failed' });
    }
  }

  async getUserById(req, res) {
    try {
      const userId = req.params.id;
      const user = await userService.getUserById(userId);
      res.status(200).json(user);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllUsers(req, res) {
    try {
      const users = await userService.getAllUsers();
      res.status(200).json(users);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateUser(req, res) {
    try {
      const userId = req.params.id;
      const userData = req.body; 
      const updatedUser = await userService.updateUser(userId, userData);
      res.status(200).json(updatedUser);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteUser(req, res) {
    try {
      const userId = req.params.id;
      const deletedUser = await userService.deleteUser(userId);
      res.status(200).json(deletedUser);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async addRole(req, res) {
    try {
      const userId = req.params.id;
      const {name} = req.body; 
      const updatedUser = await userService.addUserRole(userId, name.toUpperCase());
      res.status(200).json(updatedUser);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
  async removeRole(req, res) {
    try {
      const userId = req.params.id;
      const {name} = req.body; 
      const updatedUser = await userService.removeUserRole(userId, name.toUpperCase());
      res.status(200).json(updatedUser);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateUserPassword(req, res) {
    try {
      const userId = req.params.id;
      const {oldPassword,newPassword} = req.body; 
      const updatedUser = await userService.updatePassword(userId,oldPassword,newPassword);
      res.status(200).json(updatedUser);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}


module.exports = new UserController();
