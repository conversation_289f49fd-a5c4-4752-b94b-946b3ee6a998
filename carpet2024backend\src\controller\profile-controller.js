const profileService = require('../services/profile-service');

class ProfileController {
  async createProfile(req, res) {
    try {
      const profileData = req.body;
      const profile = await profileService.createProfile(profileData);
      res.status(201).json(profile);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getProfileById(req, res) {
    try {
      const profileId = req.params.id;
      const profile = await profileService.getProfileById(profileId);
      res.status(200).json(profile);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllProfiles(req, res) {
    try {
      const profiles = await profileService.getAllProfiles();
      res.status(200).json(profiles);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateProfile(req, res) {
    try {
      const profileId = req.params.id;
      const profileData = req.body;
      const updatedProfile = await profileService.updateProfile(profileId, profileData);
      res.status(200).json(updatedProfile);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteProfile(req, res) {
    try {
      const profileId = req.params.id;
      const deletedProfile = await profileService.deleteProfile(profileId);
      res.status(200).json(deletedProfile);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new ProfileController();
