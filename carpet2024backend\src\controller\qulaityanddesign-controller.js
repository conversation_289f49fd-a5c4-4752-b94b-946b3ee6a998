// qualityAndDesignController.js

const QualityAndDesignService = require('../services/qualityanddesign-service');

class QualityAndDesignController {
  async createQualityAndDesign(req, res) {
    try {
      const qualityAndDesign = await QualityAndDesignService.createQualityAndDesign(req.body);
      res.status(201).json(qualityAndDesign);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getQualityAndDesignById(req, res) {
    try {
      const qualityAndDesignId = req.params.id;
      const qualityAndDesign = await QualityAndDesignService.getQualityAndDesignById(qualityAndDesignId);
      res.status(200).json(qualityAndDesign);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllQualityAndDesigns(req, res) {
    try {
      const qualityAndDesigns = await QualityAndDesignService.getAllQualityAndDesigns();
      res.status(200).json(qualityAndDesigns);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updateQualityAndDesign(req, res) {
    try {
      const qualityAndDesignId = req.params.id;
      const qualityAndDesignData = req.body;
      const updatedQualityAndDesign = await QualityAndDesignService.updateQualityAndDesign(qualityAndDesignId, qualityAndDesignData);
      res.status(200).json(updatedQualityAndDesign);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deleteQualityAndDesign(req, res) {
    try {
      const qualityAndDesignId = req.params.id;
      await QualityAndDesignService.deleteQualityAndDesign(qualityAndDesignId);
      res.status(204).end();
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new QualityAndDesignController();
