// controllers/wholeSellerController.js

const wholeSellerService = require('../services/wholeseller-service');

async function createWholeSeller(req, res) {
    try {
        const wholeSellerData = req.body;
        const wholeSeller = await wholeSellerService.createWholeSeller(wholeSellerData);
        res.status(201).json(wholeSeller);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

async function getWholeSellerById(req, res) {
    try {
        const wholeSellerId = req.params.id;
        const wholeSeller = await wholeSellerService.getWholeSellerById(wholeSellerId);
        res.status(200).json(wholeSeller);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

async function getAllWholeSellers(req, res) {
    try {
        const wholeSellers = await wholeSellerService.getAllWholeSellers();
        res.status(200).json(wholeSellers);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

async function updateWholeSeller(req, res) {
    try {
        const wholeSellerId = req.params.id;
        const wholeSellerData = req.body;
        const updatedWholeSeller = await wholeSellerService.updateWholeSeller(wholeSellerId, wholeSellerData);
        res.status(200).json(updatedWholeSeller);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

async function deleteWholeSeller(req, res) {
    try {
        const wholeSellerId = req.params.id;
        await wholeSellerService.deleteWholeSeller(wholeSellerId);
        res.status(204).send();
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
}

module.exports = {
    createWholeSeller,
    getWholeSellerById,
    getAllWholeSellers,
    updateWholeSeller,
    deleteWholeSeller
};
