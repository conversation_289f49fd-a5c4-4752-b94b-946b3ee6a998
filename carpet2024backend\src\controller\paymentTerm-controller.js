const paymentTermsService = require('../services/paymentTerm-service');

class PaymentTermsController {
  async createPaymentTerm(req, res) {
    try {
      const { name } = req.body; 
      const paymentTermData = { name }; 
      const paymentTerm = await paymentTermsService.createPaymentTerm(paymentTermData);
      res.status(201).json(paymentTerm);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getPaymentTermById(req, res) {
    try {
      const paymentTermId = req.params.id;
      const paymentTerm = await paymentTermsService.getPaymentTermById(paymentTermId);
      res.status(200).json(paymentTerm);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async getAllPaymentTerms(req, res) {
    try {
      const paymentTerms = await paymentTermsService.getAllPaymentTerms();
      res.status(200).json(paymentTerms);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async updatePaymentTerm(req, res) {
    try {
      const paymentTermId = req.params.id;
      const { name } = req.body;
      const paymentTermData = { name }; // Assuming you send the updated payment term data in the request body
      const updatedPaymentTerm = await paymentTermsService.updatePaymentTerm(paymentTermId, paymentTermData);
      res.status(200).json(updatedPaymentTerm);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async deletePaymentTerm(req, res) {
    try {
      const paymentTermId = req.params.id;
      const deletedPaymentTerm = await paymentTermsService.deletePaymentTerm(paymentTermId);
      res.status(200).json(deletedPaymentTerm);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}

module.exports = new PaymentTermsController();
