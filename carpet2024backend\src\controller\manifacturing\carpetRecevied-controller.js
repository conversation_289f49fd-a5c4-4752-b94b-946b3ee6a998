const carpetReceivedService = require('../../services/manifacturing/carpetRecevied-service');
const path = require('path');

const createCarpetReceived = async (req, res) => {
  try {
    const { K, receivingDate, issueNo, weaverNumber,  receiveNo, issueDetails, area, amount, pcs } = req.body;

    let fileUrl = '';

    if (req.files && req.files.length > 0) {
      const uploadedFilePath = req.files[0].path;
      const fileName = path.basename(uploadedFilePath);
      fileUrl = `/public/all-files/${fileName}`;
    }

    const newCarpetReceived = await carpetReceivedService.createCarpetReceived({
      K,
      receivingDate,
      issueNo,
      weaverNumber,
      // weight,
      // yes,
      // no,
      receiveNo,
      issueDetails,
      area, // add area
      amount, // add amount
      pcs // add pcs
    });
    if(newCarpetReceived){
      // Update PCS fields for the related issue
      if (issueNo && issueNo._id) {
        const carpetOrderService = require('../../services/manifacturing/carpetOrder-service');
        try {
          await carpetOrderService.updatePCSFields(issueNo._id);
        } catch (error) {
          console.error('Error updating PCS fields:', error);
        }
      }

      res.status(201).json(newCarpetReceived);
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllCarpetReceiveds = async (req, res) => {
  try {
    const carpetReceiveds = await carpetReceivedService.getAllCarpetReceiveds();
    res.status(200).json(carpetReceiveds);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getCarpetReceivedById = async (req, res) => {
  try {
    const carpetReceived = await carpetReceivedService.getCarpetReceivedById(req.params.id);
    if (!carpetReceived) {
      return res.status(404).json({ message: "CarpetReceived not found" });
    }
    res.status(200).json(carpetReceived);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updateCarpetReceived = async (req, res) => {
  try {
    const { K, receivingDate, issueNo, weaverNumber, weight, yes, no, receiveNo , issueDetails} = req.body;
    let fileUrl = '';

    if (req.files && req.files.length > 0) {
      const uploadedFilePath = req.files[0].path;
      const fileName = path.basename(uploadedFilePath);
      fileUrl = `/public/all-files/${fileName}`;
    }

    const updatedCarpetReceivedData = {
      K,
      receivingDate,
      issueNo,
      weaverNumber,
      weight,
      yes,
      no,
      receiveNo,
      issueDetails,
     // uploadFile: fileUrl,
    };

    const updatedCarpetReceived = await carpetReceivedService.updateCarpetReceived(req.params.id, updatedCarpetReceivedData);

    if (!updatedCarpetReceived) {
      return res.status(404).json({ message: "CarpetReceived not found" });
    }

    res.status(200).json(updatedCarpetReceived);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deleteCarpetReceived = async (req, res) => {
  try {
    // Get the carpet data before deleting to update PCS fields
    const carpetToDelete = await carpetReceivedService.getCarpetReceivedById(req.params.id);

    const deletedCarpetReceived = await carpetReceivedService.deleteCarpetReceived(req.params.id);
    if (!deletedCarpetReceived) {
      return res.status(404).json({ message: "CarpetReceived not found" });
    }

    // Update PCS fields for the related issue after deletion
    if (carpetToDelete && carpetToDelete.issueNo && carpetToDelete.issueNo._id) {
      const carpetOrderService = require('../../services/manifacturing/carpetOrder-service');
      try {
        await carpetOrderService.updatePCSFields(carpetToDelete.issueNo._id);
      } catch (error) {
        console.error('Error updating PCS fields after deletion:', error);
      }
    }

    res.status(200).json({ message: "CarpetReceived deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get received carpets by issue IDs
const getReceivedCarpetsByIssueIds = async (req, res) => {
  try {
    console.log('getReceivedCarpetsByIssueIds called');
    console.log('Request body:', req.body);

    const { issueIds } = req.body;
    if (!issueIds || !Array.isArray(issueIds)) {
      console.log('Invalid issueIds:', issueIds);
      return res.status(400).json({ message: 'Issue IDs array is required' });
    }

    console.log('Fetching received carpets for issue IDs:', issueIds);
    const receivedCarpets = await carpetReceivedService.getReceivedCarpetsByIssueIds(issueIds);
    console.log('Found received carpets:', receivedCarpets.length);
    res.status(200).json(receivedCarpets);
  } catch (error) {
    console.error('Error in getReceivedCarpetsByIssueIds:', error);
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  createCarpetReceived,
  getAllCarpetReceiveds,
  getCarpetReceivedById,
  updateCarpetReceived,
  deleteCarpetReceived,
  getReceivedCarpetsByIssueIds
};
