// controller/container-rev-controller.js

const ContainerReceived = require("../model/phase-1/container-rvc");
const containerReceivedService = require("../services/container-rev-service");

async function createContainerReceived(req, res) {
  try {
    const containerReceivedData = req.body;
    const containerReceived =
      await containerReceivedService.createContainerReceived(
        containerReceivedData
      );
    res.status(201).json(containerReceived);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function getContainerReceivedById(req, res) {
  try {
    const containerReceivedId = req.params.id;
    const containerReceived =
      await containerReceivedService.getContainerReceivedById(
        containerReceivedId
      );
    res.status(200).json(containerReceived);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function getAllContainerReceived(req, res) {
  try {
    const containerReceived =
      await containerReceivedService.getAllContainerReceived();
    res.status(200).json(containerReceived);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function updateContainerReceived(req, res) {
  try {
    const containerReceivedId = req.params.id;
    const containerReceivedData = req.body;
    const updatedContainerReceived =
      await containerReceivedService.updateContainerReceived(
        containerReceivedId,
        containerReceivedData
      );
    res.status(200).json(updatedContainerReceived);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function deleteContainerReceived(req, res) {
  try {
    const containerReceivedId = req.params.id;
    await containerReceivedService.deleteContainerReceived(containerReceivedId);
    res.status(204).send();
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}

async function stockDetail(req, res) {
  try {
    

    let carpetData = await containerReceivedService.stockDetails(req);
    return res.status(200).send({
      message: "carpet stock details",
      success: true,
      data: carpetData,
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
}
async function getCompleteStockDetails(req, res) {
  try {
      const stockDetails = await containerReceivedService.getCompleteStockDetails();
      res.status(200).json(stockDetails);
  } catch (error) {
      res.status(400).json({ message: error.message });
  }
}
async function getAllCarpetStorck(req,res){
    let data =await  ContainerReceived.aggregate([
        { $unwind: "$containerItem" },
        {
          $match: {
            "containerItem.Status": { $ne: "sale" },  // Ensure the status is not "sale"
            $expr: {
              $and: [
                // Conditional match for QualityDesign
                {
                  $or: [
                    { $eq: [ "$containerItem.QualityDesign", "SAKKI MIR" ] }, // Provided criteria
                    { $eq: [ "$containerItem.QualityDesign", "$$REMOVE" ] }  // No criteria provided
                  ]
                },
                // Conditional match for Color
                {
                  $or: [
                    { $eq: [ "$containerItem.Color", "creme-braun" ] }, // Provided criteria
                    { $eq: [ "$containerItem.Color", "$$REMOVE" ] }     // No criteria provided
                  ]
                },
                // Conditional match for Size
                {
                  $or: [
                    {
                      $and: [
                        // Extract and trim height and check the range
                        {
                          $and: [
                            {
                              $gte: [
                                { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 0] }, chars: " " } } },
                                65
                              ]
                            },
                            {
                              $lte: [
                                { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 0] }, chars: " " } } },
                                76
                              ]
                            }
                          ]
                        },
                        // Extract and trim width and check the range
                        {
                          $and: [
                            {
                              $gte: [
                                { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 1] }, chars: " " } } },
                                140
                              ]
                            },
                            {
                              $lte: [
                                { $toInt: { $trim: { input: { $arrayElemAt: [{ $split: ["$containerItem.Size", " X"] }, 1] }, chars: " " } } },
                                150
                              ]
                            }
                          ]
                        }
                      ]
                    },
                    { $eq: [ "$containerItem.Size", "$$REMOVE" ] }  // No criteria provided
                  ]
                }
              ]
            }
          }
        },
        {
          $group: {
            _id: "$_id",
            impoterName: { $first: "$impoterName" },
            expensesAmount: { $first: "$expensesAmount" },
            totalArea: { $first: "$totalArea" },
            espPrice: { $first: "$espPrice" },
            blPdf: { $first: "$blPdf" },
            containerItem: { $push: "$containerItem" }
          }
        }
      ]);
      return res.status(200).send({
        message: "carpet stock details",
        success: true,
        data: data,
      });
    
      
}
module.exports = {
  createContainerReceived,
  getContainerReceivedById,
  getAllContainerReceived,
  updateContainerReceived,
  deleteContainerReceived,
  stockDetail,
  getAllCarpetStorck,
  getCompleteStockDetails // New function exported
};
