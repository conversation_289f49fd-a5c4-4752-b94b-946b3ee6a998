// controllers/impoterController.js
const {
  ImpoterService,
  AddOnPriceUpdateHistoryService,
} = require("../services/impoter-service");

var { ObjectId } = require("mongodb");

class ImpoterController {
  constructor() {
    this.impoterService = new ImpoterService();
    this.AddOnPriceUpdateHistoryService = new AddOnPriceUpdateHistoryService();
  }

  async createImpoter(req, res) {
    try {
      const impoterData = req.body;
      const newImpoter = await this.impoterService.createImpoter(impoterData);
      res.status(201).json(newImpoter);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getAllImpoters(req, res) {
    try {
      const impoters = await this.impoterService.getAllImpoters();
      res.json(impoters);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getImpoterById(req, res) {
    try {
      const id = req.params.id;
      const impoter = await this.impoterService.getImpoterById(id);
      if (!impoter) {
        res.status(404).json({ message: "Impoter not found" });
      } else {
        res.json(impoter);
      }
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async updateAddOnPrice(req, res) {
    try {
      const { importerId, addOnPriceId } = req.params;

      const updateHisObj = {};
      updateHisObj["importer_id"] = importerId;
      updateHisObj["addOnPrice_id"] = addOnPriceId;
      updateHisObj["newAddOnPrice"] = req.body;

      console.log("----imp==");

      const findImporter = await this.impoterService.getImpoterById(importerId);

      let query = { "addOnPrice._id": new ObjectId(addOnPriceId) };

      const findAddOnPrice = await this.impoterService.getAddOnPriceByIdService(
        query
      );

      console.log("-----Add---");

      updateHisObj["oldAddOnPrice"] = findAddOnPrice.addOnPrice.find(
        (item) => addOnPriceId == item._id
      ); // This data get using addOnPriceId from importer collection
      updateHisObj["createdAt"] = Date.now();

      let updateQuery = {
        _id: new ObjectId(importerId),
        "addOnPrice._id": new ObjectId(addOnPriceId),
      };
      let updatedData = {
        $set: {
          "addOnPrice.$.toDate": req.body.toDate,
          "addOnPrice.$.fromDate": req.body.fromDate,
          "addOnPrice.$.quality": req.body.quality,
          "addOnPrice.$.design": req.body.design,
          "addOnPrice.$.orderPrice": req.body.orderPrice,
        },
      };

      // Update Importer AddOnPrice
      const updateAddOnPrice = await this.impoterService.UpdateAddOnPrice(
        updateQuery,
        updatedData
      );

      console.log("---Update Import", updateAddOnPrice);

      const createUpdateHistory =
        await this.AddOnPriceUpdateHistoryService.createAddOnPriceUpdateHistory(
          updateHisObj
        );

      console.log("---Update history", createUpdateHistory);

      res.status(200).json(createUpdateHistory);
    } catch (error) {
      console.error("Error updating impoter:", error);
      res.status(500).json({ message: "Internal Server Error" });
    }
  }

  async getAddOnPriceHistory(req, res) {
    try {
      const query = { addOnPrice_id: req.params.addOnPriceId };

      const getHistory =
        await this.AddOnPriceUpdateHistoryService.getAddOnPriceUpdateHistory(
          query
        );

      res.status(200).json(getHistory);
    } catch (error) {
      return error;
    }
  }

  async updateById(req, res) {
    try {
      const { importerId, addOnPriceId } = req.params;

      const updateHisObj = {};
      updateHisObj["importer_id"] = importerId;
      updateHisObj["importer_id"] = addOnPriceId;
      updateHisObj["newAddOnPrice"] = req.body;

      console.log("----imp==");

      const findImporter = await this.impoterService.getImpoterById(importerId);

      console.log("-----Add---", findImporter);

      // const findImporter = await this.impoterService.getImpoterById(importerId);

      updateHisObj["oldAddOnPrice"] = // This data get using addOnPriceId from importer collection
        updateHisObj["createdAt"] = Date.now();

      const createUpdateHistory =
        await this.updateHistoryService.createAddOnPriceHistory(updateHisObj);

      res.status(200).json(findImporter);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async deleteImpoter(req, res) {
    try {
      const id = req.params.id;
      await this.impoterService.deleteImpoter(id);
      res.json({ message: "Impoter deleted successfully" });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  //////////
  ////// here code for add on price after add importer
  ///

  async updatePrice(req, res) {
    try {
      let id = req.params.id;
      let bodyData = req.body.addOnPrice;
      let updatedData = await this.impoterService.updateImpoterPrice(
        id,
        bodyData
      );
      if (updatedData.acknowledged == true && updatedData.modifiedCount == 1) {
        return res.status(200).send({
          message: "Price add success",
          success: true,
        });
      }
      
      return res.status(200).send({
        message: "Failed!, Something went wrong",
        success: false,
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}
module.exports = ImpoterController;
