const purchaserDetailsService = require('../services/purchaserDetails-service');

const createpurchaserDetails = async (req, res) => {
  try {
    const { group, subGroup, Material, count, partyname, address, zipcode, contractno, email, gstno, panno } = req.body;

    const newpurchaserDetails = await purchaserDetailsService.createpurchaserDetails({
      group,
      subGroup,      
      Material,
      count,
      partyname,
      address,
      zipcode,
      contractno,
      email,
      gstno,
      panno
    });



    res.status(201).json(newpurchaserDetails);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllpurchaserDetails = async (req, res) => {
  try {
    const purchaserDetails = await purchaserDetailsService.getAllpurchaserDetails();
    res.status(200).json(purchaserDetails);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getpurchaserDetailsById = async (req, res) => {
  try {
    const purchaserDetails = await purchaserDetailsService.getpurchaserDetailsById(req.params.id);
    if (!purchaserDetails) {
      return res.status(404).json({ message: "purchaser details not found" });
    }
    res.status(200).json(purchaserDetails);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updatepurchaserDetails = async (req, res) => {
  try {
    const { group,Material, subGroup, count, partyname, address, zipcode, contractno, email, gstno, panno } = req.body;

    const updatedpurchaserDetails = await purchaserDetailsService.updatepurchaserDetails(req.params.id, {
      group,
      subGroup,
      count,
      Material,
      partyname,
      address,
      zipcode,
      contractno,
      email,
      gstno,
      panno
    });

    if (!updatedpurchaserDetails) {
      return res.status(404).json({ message: "purchaser details not found" });
    }

    res.status(200).json(updatedpurchaserDetails);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deletepurchaserDetails = async (req, res) => {
  try {
    const deletedpurchaserDetails = await purchaserDetailsService.deletepurchaserDetails(req.params.id);
    if (!deletedpurchaserDetails) {
      return res.status(404).json({ message: "purchaser details not found" });
    }
    res.status(200).json({ message: "purchaser details deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createpurchaserDetails,
  getAllpurchaserDetails,
  getpurchaserDetailsById,
  updatepurchaserDetails,
  deletepurchaserDetails
};
