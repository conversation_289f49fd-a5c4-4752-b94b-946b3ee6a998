// controllers/purchaseController.js
const purchaseService = require('../../services/manifacturing/purchase-service');

const createPurchase = async (req, res) => {
  try {
    const purchaseData = req.body;
    const newPurchase = await purchaseService.createPurchase(purchaseData);
    res.status(201).json(newPurchase);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getAllPurchases = async (req, res) => {
  try {
    const purchases = await purchaseService.getAllPurchases();
    res.status(200).json(purchases);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getPurchaseById = async (req, res) => {
  try {
    const purchase = await purchaseService.getPurchaseById(req.params.id);
    if (!purchase) {
      return res.status(404).json({ message: "Purchase not found" });
    }
    res.status(200).json(purchase);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const updatePurchase = async (req, res) => {
  try {
    const purchaseData = req.body;
    const updatedPurchase = await purchaseService.updatePurchase(req.params.id, purchaseData);
    if (!updatedPurchase) {
      return res.status(404).json({ message: "Purchase not found" });
    }
    res.status(200).json(updatedPurchase);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const deletePurchase = async (req, res) => {
  try {
    const deletedPurchase = await purchaseService.deletePurchase(req.params.id);
    if (!deletedPurchase) {
      return res.status(404).json({ message: "Purchase not found" });
    }
    res.status(200).json({ message: "Purchase deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  createPurchase,
  getAllPurchases,
  getPurchaseById,
  updatePurchase,
  deletePurchase,
};
